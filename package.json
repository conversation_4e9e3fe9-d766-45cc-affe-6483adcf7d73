{"name": "dse-video", "version": "1.4.0", "private": true, "scripts": {"preinstall": "npm set registry http://************:9999/repository/npm-public/", "serve": "vue-cli-service serve", "build": "vue-cli-service build", "lint": "vue-cli-service lint"}, "dependencies": {"DseLogin": "^2.1.2", "axios": "^1.7.2", "core-js": "^3.8.3", "dayjs": "^1.11.13", "dse-video-player": "^1.0.6", "echarts": "^5.6.0", "el-tree-transfer": "^2.4.7", "element-dse-ui": "^1.0.2", "encrypt-md5": "^1.0.0", "jsencrypt": "^3.3.2", "konva": "^9.3.18", "lodash": "^4.17.21", "qs": "^6.13.0", "vue": "^2.6.14", "vue-easy-lightbox": "^0.23.0", "vue-router": "^3.6.5", "vuex": "^3.6.2", "wl-core": "^1.1.9"}, "devDependencies": {"@babel/core": "^7.12.16", "@babel/eslint-parser": "^7.12.16", "@vue/cli-plugin-babel": "~5.0.0", "@vue/cli-plugin-eslint": "~5.0.0", "@vue/cli-service": "~5.0.0", "eslint": "^7.32.0", "eslint-plugin-vue": "^8.0.3", "node-sass": "^7.0.0", "sass-loader": "^13.0.0", "vue-template-compiler": "^2.6.14"}, "eslintConfig": {"root": true, "env": {"node": true}, "extends": ["plugin:vue/essential", "eslint:recommended"], "parserOptions": {"parser": "@babel/eslint-parser"}, "rules": {}}, "browserslist": ["> 1%", "last 2 versions", "not dead"]}