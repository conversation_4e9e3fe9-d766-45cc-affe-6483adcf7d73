/**
 * @description  框架外置配置文件，用于书写项目中需要在生产环境中修改的配置信息。 本文件在打包时不会参与编译，为了保证各个
 * 浏览器的兼容性，请使用ES5语法编写本配置文件信息.
 * <AUTHOR> <EMAIL>
 * @version v1.2.2
 * @date create Administrator on 2024/12/16
 *
 */
'use strict';

//
// var baseUrl = "//***********:8081";
// var themeTemplate = "/templates/custom";
// var themeTemplate = "/templates/default";//devop
// var themeTemplate = "."; //DEV

// 公共方法
var getIndexUrl = function (app) {
  var indexPath = '';
  if (app) {
    indexPath = location.protocol + '//' + location.host + app;
  } else {
    indexPath = location.protocol + '//' + location.host;
  }
  return indexPath;
};

var DSE = {};
//
// 配置接口认证服务类型

// 是否采用TokenServer , true 为TokenServer , false 为 Cas Server //重构登录部分，DSE.useTokenServer设置恒为 true
DSE.useTokenServer = true;
// 基础平台项目名称
DSE.videoSysTitle = '视频管理';
document.title = '视频管理';

// 接口前缀  之前的参数，为了兼容，所以先保留
const urlPrefix = 'baseplatform/rs';
DSE.urlPrefix = urlPrefix;

//之前的参数，为了兼容，所以先保留
DSE.useTokenServer = true;

//点击登出时，是否使服务器token失效 true:是 false：否，仅清空本地token
DSE.invalidToken = false;

//跳转用户自定义的登录页
DSE.userLoginFailureJumpUrl = window.location.protocol + '//' + window.location.hostname + ':' + window.location.port + '/gqglpt-web/' + "login"

//登录成功后跳转带头部导航的自定义地址
DSE.userHome = window.location.protocol + '//' + window.location.hostname + ':' + window.location.port + '/bpweb/' + "main3"

//跳转NotFound
DSE.NotFound = window.location.protocol + '//' + window.location.hostname + ':' + window.location.port + '/' + "notfound"

//token认证服务器地址
DSE.tokenServer = 'oauth/token';

// 检测token认证服务器地址
DSE.checkTokenServer = 'oauth/check_token';

//登出时候 废除当前token
DSE.revokeTokenServer = 'oauth/revoke';

// 接口传参是否启用md5加密
DSE.isMd5Encrypt = true;

// 视频业务接口 前缀
DSE.videoUrlPrefix = 'video';
// 萤石云接口 前缀
DSE.yingShiUrlPrefix = 'yingShi';

// 海康插件所在位置，不填默认是 /static/h5player/
DSE.h5playerBasePath = '/dse-video-web/static/h5player/';

// 大华rtsp播放插件位置，不填默认 './static/dhPro/'
DSE.daHuaRtspPrefixUrl = '/dse-video-web/static/dhPro/';


// 星火视频播放插件位置，不填默认 '/static/xingHuo/decoder-pro.js'
DSE.xingHuoVideoPrefixUrl = '/dse-video-web/static/xingHuo/decoder-pro.js'

// 是否启用双击目录打开所有视频  true:是 false：否
DSE.doubleClickOpenAllVideo = true;

// 是否启用双击点击才打开视频  true:是 false：否
DSE.doubleClickOpenVideo = true;

// 海康播放插件下载地址
DSE.haiKangPluginDownUrl = '/dse-video-web/static/haiKangPlugin/VideoWebPlugin.exe';

DSE.clientUser = 'dse1';
DSE.clientPwd = '123456';
DSE.authType = 'unpd';


DSE.haiKangServiceConfig = {
  appKey: '27371516', // 海康应用k
  appSecret: 'BdCNbVg3AD9FR1L4oycC',
  enableHTTPS: '1',
  host: '444',
  ip: '***************',
  snapDir: '/home/<USER>/videoDir',
  videoDir: '/home/<USER>/videoDir',
  mainStreamReqWinNum: 1,
};

window.DSE = DSE;
