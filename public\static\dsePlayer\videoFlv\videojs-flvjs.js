(function(f){if(typeof exports==="object"&&typeof module!=="undefined"){module.exports=f()}else if(typeof define==="function"&&define.amd){define([],f)}else{var g;if(typeof window!=="undefined"){g=window}else if(typeof global!=="undefined"){g=global}else if(typeof self!=="undefined"){g=self}else{g=this}g.videojsFlvjs = f()}})(function(){var define,module,exports;return (function(){function r(e,n,t){function o(i,f){if(!n[i]){if(!e[i]){var c="function"==typeof require&&require;if(!f&&c)return c(i,!0);if(u)return u(i,!0);var a=new Error("Cannot find module '"+i+"'");throw a.code="MODULE_NOT_FOUND",a}var p=n[i]={exports:{}};e[i][0].call(p.exports,function(r){var n=e[i][1][r];return o(n||r)},p,p.exports,r,e,n,t)}return n[i].exports}for(var u="function"==typeof require&&require,i=0;i<t.length;i++)o(t[i]);return o}return r})()({1:[function(require,module,exports){
(function (global){(function (){
var win;

if (typeof window !== "undefined") {
    win = window;
} else if (typeof global !== "undefined") {
    win = global;
} else if (typeof self !== "undefined"){
    win = self;
} else {
    win = {};
}

module.exports = win;

}).call(this)}).call(this,typeof global !== "undefined" ? global : typeof self !== "undefined" ? self : typeof window !== "undefined" ? window : {})

},{}],2:[function(require,module,exports){
(function (global){(function (){
"use strict";

Object.defineProperty(exports, "__esModule", {
  value: true
});
exports.default = void 0;
var _video = _interopRequireDefault((typeof window !== "undefined" ? window['videojs'] : typeof global !== "undefined" ? global['videojs'] : null));
var _window = _interopRequireDefault(require("global/window"));
function _interopRequireDefault(e) { return e && e.__esModule ? e : { default: e }; }
/**
 * @file plugin.js
 */

const Html5 = _video.default.getTech('Html5');
const mergeOptions = _video.default.obj ? _video.default.obj.merge : _video.default.mergeOptions || _video.default.util.mergeOptions;
const defaults = {
  mediaDataSource: {},
  config: {}
};

/**
 * Flvjs tech that simply wires up fv.js to a Video.js tech
 *
 * @see {@link https://github.com/bilibili/flv.js|flv.js}
 */
class Flvjs extends Html5 {
  /**
   * Create an instance of this Tech.
   *
   * @param {Object} [options]
   *        The key/value store of player options.
   *
   * @param {Component~ReadyCallback} ready
   *        Callback function to call when the `Flvjs` Tech is ready.
   */
  constructor(options, ready) {
    options = mergeOptions(defaults, options);
    super(options, ready);
  }

  /**
    * Setter for the `Flvjs` Tech's source object.
    *
    * @param {Tech~SourceObject} [src]
    *        The source object to set on the `Flvjs` techs.
    */
  setSrc(src) {
    if (this.flvPlayer) {
      // Is this necessary to change source?
      this.flvPlayer.detachMediaElement();
      this.flvPlayer.destroy();
    }
    const mediaDataSource = this.options_.mediaDataSource;
    const config = this.options_.config;
    mediaDataSource.type = mediaDataSource.type === undefined ? 'flv' : mediaDataSource.type;
    mediaDataSource.url = src;
    this.flvPlayer = _window.default.flvjs.createPlayer(mediaDataSource, config);
    this.flvPlayer.attachMediaElement(this.el_);
    this.flvPlayer.load();
  }

  /**
   * Dispose of flvjs.
   */
  dispose() {
    if (this.flvPlayer) {
      this.flvPlayer.detachMediaElement();
      this.flvPlayer.destroy();
    }
    super.dispose();
  }
}

/**
 * Check if the Flvjs tech is currently supported.
 *
 * @return {boolean}
 *          - True if the Flvjs tech is supported.
 *          - False otherwise.
 */
Flvjs.isSupported = function () {
  return _window.default.flvjs && _window.default.flvjs.isSupported();
};

/**
 * Flvjs supported mime types.
 *
 * @constant {Object}
 */
Flvjs.formats = {
  'video/flv': 'FLV',
  'video/x-flv': 'FLV'
};

/**
 * Check if the tech can support the given type
 *
 * @param {string} type
 *        The mimetype to check
 * @return {string} 'probably', 'maybe', or '' (empty string)
 */
Flvjs.canPlayType = function (type) {
  if (Flvjs.isSupported() && type in Flvjs.formats) {
    return 'maybe';
  }
  return '';
};

/**
 * Check if the tech can support the given source
 *
 * @param {Object} srcObj
 *        The source object
 * @param {Object} options
 *        The options passed to the tech
 * @return {string} 'probably', 'maybe', or '' (empty string)
 */
Flvjs.canPlaySource = function (srcObj, options) {
  return Flvjs.canPlayType(srcObj.type);
};

// Include the version number.
Flvjs.VERSION = '0.2.0';
_video.default.registerTech('Flvjs', Flvjs);
var _default = exports.default = Flvjs;

}).call(this)}).call(this,typeof global !== "undefined" ? global : typeof self !== "undefined" ? self : typeof window !== "undefined" ? window : {})

},{"global/window":1}]},{},[2])(2)
});

//# sourceMappingURL=data:application/json;charset=utf-8;base64,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
