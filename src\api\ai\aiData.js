import { requestApi } from "@/api/index";

let urlPrefix = window.DSE.videoUrlPrefix + "/ai";


// 获取监控点配置
export async function getAlgorithmCofRequest(id) {
  return requestApi.getData(`${urlPrefix}/conf/${id}`);
}
// 开启/关闭 实时算法
export async function realtimeConfRequest(id) {
  return requestApi.putData(`${urlPrefix}/conf/realtime/${id}`);
}
// 开启/关闭 告警算法
export async function alarmConfRequest(id) {
  return requestApi.putData(`${urlPrefix}/conf/alarm/${id}`);
}
// 获取算法列表
export async function algorithmListRequest() {
  return requestApi.getData(`${urlPrefix}/conf/algorithmList`);
}
// 新增或修改
export async function saveOrUpdateAlgorithmRequest(data) {
  return requestApi.postData(`${urlPrefix}/conf/saveOrUpdate`, data);
}

