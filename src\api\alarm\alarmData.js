import { requestApi } from "@/api/index";

let urlPrefix = window.DSE.videoUrlPrefix + "/alarm";

// 分页获取告警记录
export async function getAlarmListRequest(data) {
  return requestApi.postData(`${urlPrefix}/list`, data);
}

// 统计告警记录
export async function getAlarmCountRequest(data) {
  return requestApi.postData(`${urlPrefix}/count`, data);
}
// 修改告警记录状态
export async function updateAlarmRequest(data) {
  return requestApi.postData(`${urlPrefix}/update`, data);
}
// 删除记录
export async function deleteSiteRecord(id) {
  return requestApi.deleteData(`${urlPrefix}/delete/${id}`);
}
