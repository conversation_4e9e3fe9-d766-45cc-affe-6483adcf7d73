import { requestApi } from "@/api/index";

let urlPrefix = window.DSE.urlPrefix + "/baseInfo";


export const QUERY_ORGANIZATION_TREE_4S_URL = `${urlPrefix}/organization/queryWholeOrganizationTree4S`;

// 行政区划管理树(新建组织机构时使用)
export async function QueryWholeDivisionTree4S(queryObject) {
  return requestApi.postData(`${urlPrefix}/division/queryWholeDivisionTree4S`, queryObject);
}

// 获取管理单位树
export async function getOrgTreeRequest(queryObject) {
  return requestApi.postData(QUERY_ORGANIZATION_TREE_4S_URL, queryObject);
}
