import { requestApi } from "@/api/index";

let urlPrefix = window.DSE.videoUrlPrefix + "/common";

let COMMON_CONST_BY_CODE = `${urlPrefix}/const/`

//获取可用协议
export async function availableProtocolRequest() {
  return requestApi.getData(`${urlPrefix}/availableProtocol`);
}
// 获取工程列表
export async function projectListRequest() {
  return requestApi.getData(`${urlPrefix}/projectList`);
}

// 检查集成的插件
export async function pluginsCommonRequest() {
  return requestApi.getData(`${urlPrefix}/plugins`);
}

// 获取常量枚举, 可选值: cameraType, serverType, videoStatus
export async function commonPageRequest(code) {
  return requestApi.getData(`${urlPrefix}/const/${code}`);
}

// 新增字典项
export async function queryDictionary4Sel(type, addAll) {
  //将对象转化为下拉框所需的树类型;
  //缓存字典项数据，减少数据请求
  let results = JSON.parse(localStorage.getItem(type));
  if (results && results.length > 0) {
    return results;
  } else {
    results = [];
    let response = await requestApi.getData(COMMON_CONST_BY_CODE + type);
    let dictData = response.data || [];
    if(dictData && dictData.length) {
      dictData.forEach(d => {
        results.push({
          label: d.value,
          value: d.key
        })
      })
    }
    localStorage.setItem(type, JSON.stringify(results));
    return results;
  }
}

// 根据字典项值获取对应的label
export async function getDictLabelByValue(keyField, keyValue) {
  let tempArray = JSON.parse(localStorage.getItem(keyField));
  if (!tempArray) {
    let queryObject = {
      codes: keyField
    };
    tempArray = await queryDictionary4Sel(queryObject) || [];
  }
  let resultItems = tempArray.filter((item) => {
    return item.value === keyValue;
  });
  return resultItems.length > 0 ? resultItems[0].label : "";
}