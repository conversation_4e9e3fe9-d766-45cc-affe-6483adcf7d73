import { requestApi } from '@/api/index'

let urlPrefix = window.DSE.urlPrefix + "/dataPerm";

// 获取权限服务配置列表
export async function PermConfListRequest(queryObject) {
  return requestApi.getData(`${urlPrefix}/conf/list`, queryObject);
}

// 保存权限服务配置
export async function PermConfSaveRequest(queryObject) {
  return requestApi.postData(`${urlPrefix}/conf/save`, queryObject);
}


// 修改权限服务配置
export async function PermConfUpdateRequest(queryObject) {
  return requestApi.postData(`${urlPrefix}/conf/update`, queryObject);
}

// 删除权限服务配置
export async function deleteConfPermRequest(id) {
  return requestApi.deleteData(`${urlPrefix}/conf/${id}`);
}

// 分页查询已授权的用户
export async function pageUserListRequest(queryObject) {
  return requestApi.postData(`${urlPrefix}/user/page`, queryObject);
}

// 查询已授权的用户
export async function pageAllUserListRequest(queryObject) {
  return requestApi.postData(`${urlPrefix}/user/list`, queryObject);
}

// 通过dataId绑定数据权限
export async function bindByDataIdRequest(queryObject) {
  return requestApi.postData(`${urlPrefix}/bindByDataId`, queryObject);
}

// 通过permId绑定数据权限
export async function bindByPermIdRequest(queryObject) {
  return requestApi.postData(`${urlPrefix}/bindByPermId`, queryObject);
}

// 解绑用户数据权限
export async function unbindAuthRequest(queryObject) {
  return requestApi.postData(`${urlPrefix}/unbind`, queryObject);
}


// 复制用户数据权限
export async function copyPermAuthRequest(queryObject) {
  return requestApi.postData(`${urlPrefix}/copy`, queryObject);
}


// 查看用户所有的权限
export async function getDataPermByUserIdRequest(userId) {
  return requestApi.getData(`${urlPrefix}/${userId}`);
}