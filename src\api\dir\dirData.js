import { requestApi } from "@/api/index";

let urlPrefix = window.DSE.videoUrlPrefix + "/dir";

// 获取标签下的目录树
export async function queryDirTreeRequest(id) {
  return requestApi.postData(`${urlPrefix}/tree`, {tagId: id});
}

// 添加一级目录
export async function addParentDirRequest(data) {
  return requestApi.postData(`${urlPrefix}/addParentDir`, data);
}
// 添加子目录和站点
export async function addChildDirRequest(data) {
  return requestApi.postData(`${urlPrefix}/addChildDir`, data);
}

// 编辑目录保存
export async function updateDirRequest(data) {
  return requestApi.putData(`${urlPrefix}/update`, data);
}

// 添加一级目录
export async function deleteDirRequest(id) {
  return requestApi.deleteData(`${urlPrefix}/${id}`);
}


// 编辑目录-获取当前标签下未被选择的待选站点和已选站点
export async function getNotSelectSiteRequest(data) {
  return requestApi.getData(`${urlPrefix}/getNotSelectSite?tagId=${data.tagId}&dirId=${data.dirId}`);
}

// 修改同级目录顺序
export async function sortDirRequest(data) {
  return requestApi.postData(`${urlPrefix}/sort`, data);
}