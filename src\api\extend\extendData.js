import { requestApi } from "@/api/index";

let urlPrefix = window.DSE.videoUrlPrefix + "/site/ext";

// 获取权限服务配置列表
export async function getExtentConfListRequest() {
  return requestApi.getData(`${urlPrefix}/conf/list`);
}
// 保存权限服务配置
export async function saveSiteExtRequest(data) {
  return requestApi.postData(`${urlPrefix}/conf/save`, data);
}
// 修改权限服务配置
export async function updateSiteExtRequest(data) {
  return requestApi.postData(`${urlPrefix}/conf/update`, data);
}

// 分页查询已绑定的监控点
export async function getSiteExtPageRequest(data) {
  return requestApi.postData(`${urlPrefix}/site/page`, data);
}
// 查询已绑定的监控点
export async function getSiteExtListRequest(data) {
  return requestApi.postData(`${urlPrefix}/site/list`, data);
}

// 通过dataId绑定监控点扩展
export async function bindByDataIdExtRequest(data) {
  return requestApi.postData(`${urlPrefix}/bindByDataId`, data);
}
// 解绑用户监控点扩展
export async function unbindExtRequest(data) {
  return requestApi.postData(`${urlPrefix}/unbind`, data);
}
// 删除权限服务配置
export async function deleteExtConfRequest(id) {
  return requestApi.deleteData(`${urlPrefix}/conf/${id}`);
}