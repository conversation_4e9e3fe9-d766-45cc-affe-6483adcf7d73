import { requestApi } from "@/api/index";

let urlPrefix = window.DSE.videoUrlPrefix + "/play";

// 查询服务和服务下面的站点
export async function listServerAndSiteRequest(queryObject) {
  return requestApi.getData(`${urlPrefix}/common/listServerAndSite`, queryObject);
}

// 获取标签下的目录树、站点列表
export async function listTreeByTagIdRequest(tagId) {
  return requestApi.getData(`${urlPrefix}/common/listTreeByTagId?tagId=${tagId}`);
}

// 通过服务器信息获取站点信息
export async function listSiteByServerIdRequest(queryObject) {
  return requestApi.postData(`${urlPrefix}/common/listSiteByServerId`, queryObject);
}


// 手动刷新在线状态
export async function refreshOnlineStatusRequest() {
  return requestApi.postData(`${urlPrefix}/common/refreshOnlineStatus`);
}


// 海康-获取综合安防视频流url
export async function getCameraPreviewUrlRequest(data) {
  return requestApi.postData(`${urlPrefix}/hik/getCameraPreviewUrl`, data);
}

// 海康-云台控制
export async function controlCameraHikRequest(data) {
  return requestApi.postData(`${urlPrefix}/hik/controlCamera`, data);
}

// 海康-获取监控点回放取流URL
export async function getCameraPlaybackURLsRequest(data) {
  return requestApi.postData(`${urlPrefix}/hik/getCameraPlaybackURLs`, data);
}

// 海康-获取综合安防配置信息(通过cameraIndexCode获取)
export async function getSecureConfByCodeRequest() {
  return requestApi.getData(`${urlPrefix}/hik/getSecureConf`);
}

// 海康-调用海康接口
export async function inokeHaiKangRequest(data) {
  return requestApi.postData(`${urlPrefix}/hik/invoke`, data);
}


// 大华-实时预览 “HLS、FLV、RTMP实时拉流”
export async function daHuaRealTimeRequest(data) {
  return requestApi.postData(`${urlPrefix}/dh/realtime`, data);
}

// 大华-云台方向控制
export async function directOperateDhRequest(data) {
  return requestApi.postData(`${urlPrefix}/dh/directOperate`, data);
}

// 大华-云台镜头控制
export async function cameraOperateDhRequest(data) {
  return requestApi.postData(`${urlPrefix}/dh/cameraOperate`, data);
}

// 大华-录像回放
export async function replayDhRequest(data) {
  return requestApi.postData(`${urlPrefix}/dh/replay`, data);
}

// 大华-调用大华接口
export async function inokeDaHuaRequest(data) {
  return requestApi.postData(`${urlPrefix}/dh/invoke`, data);
}


// 获取萤石云token
export async function getEzvizAccessToken(serverId) {
  return requestApi.getData(`${urlPrefix}/ezviz/getAccessToken/${serverId}`);
}