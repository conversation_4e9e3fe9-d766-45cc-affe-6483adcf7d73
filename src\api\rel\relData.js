import { requestApi } from "@/api/index";

let urlPrefix = window.DSE.videoUrlPrefix + "/rel";

// 查询列表分页
export async function getVideoTagList(data) {
  return requestApi.postData(`${urlPrefix}/getVideoTagList`, data);
}

// 监控站点总数
export async function getVideoSiteCount(data) {
  return requestApi.postData(`${urlPrefix}/getVideoSiteCount`, data);
}

// 解除关联
export async function unLinkRelRequest(id) {
  return requestApi.deleteData(`${urlPrefix}/unLink?id=${id}`);
}


// 批量解除关联
export async function batchUnLinkRequest(ids) {
    return requestApi.deleteData(`${urlPrefix}/batchUnLink?ids=${ids}`);
  }
