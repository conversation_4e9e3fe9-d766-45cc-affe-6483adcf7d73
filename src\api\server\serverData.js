import { requestApi } from '@/api/index';

let urlPrefix = window.DSE.videoUrlPrefix + '/server';

// 查询列表分页
export async function PageServerRequest(queryObject) {
  return requestApi.postData(`${urlPrefix}/page`, queryObject);
}

// 查询下拉列表
export async function QueryServerListRequest(name) {
  return requestApi.getData(`${urlPrefix}/list?name=` + name);
}

// 尝试删除信息
export async function TryDeleteServerRequest(id) {
  return requestApi.getData(`${urlPrefix}/tryDelete/${id}`);
}

// 确认删除信息
export async function DeleteServerRequest(id) {
  return requestApi.deleteData(`${urlPrefix}/${id}`);
}

// 新增
export async function SaveServerRequest(queryObject) {
  return requestApi.postData(`${urlPrefix}/save`, queryObject);
}

// 修改
export async function UpdateServerRequest(queryObject) {
  return requestApi.putData(`${urlPrefix}/update`, queryObject);
}

// 是否开启服务器
export async function UpdateEnableRequest(id) {
  return requestApi.putData(`${urlPrefix}/enable/${id}`);
}

// 刷新监测点
export async function RefreshSiteRequest(serverId) {
  return requestApi.putData(`${urlPrefix}/${serverId}/refreshSite`, {
    serverId,
  });
}
