import { requestApi } from "@/api/index";

let urlPrefix = window.DSE.videoUrlPrefix + "/site";

// 查询列表分页
export async function PageSiteRequest(queryObject) {
  return requestApi.postData(`${urlPrefix}/page`, queryObject);
}

// 监控站点总数
export async function CountSiteRequest(queryObject) {
  return requestApi.postData(`${urlPrefix}/count`, queryObject);
}


// 新增监控站点
export async function SaveSiteRequest(queryObject) {
  return requestApi.postData(`${urlPrefix}/save`, queryObject);
}

// 新增监控站点
export async function UpdateSiteRequest(queryObject) {
  return requestApi.putData(`${urlPrefix}/update`, queryObject);
}

// 新增监控站点
export async function DeleteSiteRequest(id) {
  return requestApi.deleteData(`${urlPrefix}/delete/${id}`);
}
// 收藏/取消收藏
export async function collectSiteRequest(id) {
  return requestApi.putData(`${urlPrefix}/collect/${id}`);
}
// 获取监控点信息
export async function siteInfoRequest(id) {
  return requestApi.getData(`${urlPrefix}/${id}`);
}