import { requestApi } from "@/api/index";

let urlPrefix = window.DSE.videoUrlPrefix + "/stats";

// 查询列表分页
export async function ListStatsRequest(queryObject) {
  return requestApi.postData(`${urlPrefix}/list`, queryObject);
}

// 查询列表分页
export async function CountStatsRequest(data) {
  return requestApi.postData(`${urlPrefix}/count`, data);
}

// 通过监测点ID查询在线状态
export async function listStatusRequest(data) {
  return requestApi.postData(`${urlPrefix}/listStatus`, data);
}
