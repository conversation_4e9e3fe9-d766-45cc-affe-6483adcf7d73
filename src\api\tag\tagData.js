import { requestApi } from "@/api/index";

let urlPrefix = window.DSE.videoUrlPrefix + "/tag";

// 查询列表分页
export async function ListTagRequest() {
  return requestApi.getData(`${urlPrefix}/list`);
}

// 新增
export async function saveTagRequest(data) {
  return requestApi.postData(`${urlPrefix}/save`, data);
}

// 修改
export async function updateTagRequest(data) {
  return requestApi.putData(`${urlPrefix}/update`, data);
}

// 修改
export async function sortTagRequest(data) {
  return requestApi.postData(`${urlPrefix}/sort`, data);
}

// saveTagRequest
export async function deleteTagRequest(id) {
  return requestApi.deleteData(`${urlPrefix}/${id}`);
}
