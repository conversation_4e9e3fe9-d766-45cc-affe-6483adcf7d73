import qs from 'qs'
import { requestApi } from "@/api/index";

let urlPrefix = window.DSE.yingShiUrlPrefix + "/api";

// 萤石-获取综合安防视频流url
export async function getYingShiLiveAddressUrlRequest(data) {
  return requestApi.postData(`${urlPrefix}/lapp/v2/live/address/get`, qs.stringify(data), {
    headers: {
      "Content-Type": 'application/x-www-form-urlencoded',
    }
  });
}

// 萤石-开始云台控制
export async function startYingShiPtzRequest(data) {
  return requestApi.postData(`${urlPrefix}/lapp/device/ptz/start`, qs.stringify(data), {
    headers: {
      "Content-Type": 'application/x-www-form-urlencoded',
    }
  });
}

// 萤石-停止云台控制
export async function stopYingShiPtzRequest(data) {
  return requestApi.postData(`${urlPrefix}/lapp/device/ptz/stop`, qs.stringify(data), {
    headers: {
      "Content-Type": 'application/x-www-form-urlencoded',
    }
  });
}