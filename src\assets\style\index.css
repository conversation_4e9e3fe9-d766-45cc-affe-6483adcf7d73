body {
  margin: 0;
}
a, article, aside, b, body, button, dd, div, dl, dt, footer, h1, h2, h3, h4, h5, header, i, input, li, nav, p, section, select, span, textarea, ul {
  padding: 0;
  margin: 0;
  list-style: none;
  text-decoration: none;
  border: none;
  box-sizing: border-box;
  font-family: Microsoft YaHei;
  -webkit-tap-highlight-color: transparent;
  font-size: 14px;
  font-family: Microsoft YaHei, å¾®è½¯é›…é»‘;
}

@font-face {
  font-family: "DseNameTitleFont";
  src: url("@/assets/font/name-title.ttf") format("truetype"); /* 为老版本IE */
  font-weight: normal;
  font-style: normal;
}

@font-face {
  font-family: "DseDDINBold";
  src: url("@/assets/font/D-DIN-Bold.otf") format("truetype"); /* 为老版本IE */
  font-weight: normal;
  font-style: normal;
}

@font-face {
  font-family: "SourceHanSansCNBold";
  src: url("@/assets/font/SourceHanSansCN-Medium.otf") format("truetype"); /* 为老版本IE */
  font-weight: normal;
  font-style: normal;
}

* {
  scrollbar-width: thin;
}

iframe {
  border: none;
}

/*定义滚动条高宽及背景 高宽分别对应横竖滚动条的尺寸*/
/* 整个滚动条 */
::-webkit-scrollbar {
  width: 8px; /* 设置垂直滚动条的宽度 */
  height: 8px; /* 设置水平滚动条的高度 */
}

/* 滚动条轨道（背景） */
::-webkit-scrollbar-track {
  background-color: #f1f1f1; /* 滚动条轨道的背景色 */
  border-radius: 10px;
}

/* 滚动条滑块（可拖动部分） */
::-webkit-scrollbar-thumb {
  background-color: #888; /* 滚动条滑块的颜色 */
  border-radius: 10px;
  border: 2px solid #f1f1f1; /* 滑块的边框 */
}

/* 滚动条滑块在鼠标悬停时的样式 */
::-webkit-scrollbar-thumb:hover {
  background-color: #555; /* 鼠标悬停时的颜色 */
}

.el-menu--collapse .el-menu-item .submenu-item-name,
.el-menu--collapse .el-submenu > .el-submenu__title .submenu-item-name {
  height: 0;
  width: 0;
  overflow: hidden;
  visibility: hidden;
  display: inline-block;
}
.el-tree--highlight-current
  .el-tree-node:not(.is-disabled).is-current
  > .el-tree-node__content
  > .el-tree-node__label {
  background: transparent;
}

.el-tree--highlight-current
  .el-tree-node:not(.is-disabled).is-current
  > .el-tree-node__content {
  background: #e7f1fe;
}


.el-tree-select__suggestion-panel
  .el-tree-node:not(.is-disabled).is-current
  > .el-tree-node__content
  > .node-label {
  background: transparent;
  color: #0d70f2;
}

.page-wrapper {
  width: 100%;
  height: 100%;
  padding: 16px;
  box-sizing: border-box;
  background: #edf3fa;
}
.page-wrapper-dark {
  background-color: #012950;
}
.wrapper-box {
  width: 100%;
  height: 100%;
  background-color: #fff;
  box-sizing: border-box;
  border-radius: 8px;
}

.page-right-box {
  width: calc(100% - 356px);
  height: 100%;
  background: #fff;
  border-radius: 8px;
  box-sizing: border-box;
}

.oper-btn {
  cursor: pointer;
  color: #0d70f2;
}
.oper-btn + .oper-btn {
  margin-left: 12px;
}

.el-tooltip__popper {
  max-width: 600px;
}

.el-dialog {
  margin-top: 10vh !important;
}