<template>
  <div class="page-wapper">
    <div class="wapper-box">
      <el-page-header @back="goBack" :title="title" backIcon="el-icon-back">
      </el-page-header>
      <div class="page-detail-box" :class="{'page-detail-box-no-bottom': !showCencel && !showSave}">
        <slot></slot>
      </div>
      <div class="page-detail-bottom" v-if="showSave || showCencel">
        <slot name="bottom">
          <el-button type="primary" v-if="showSave" @click="saveHandle" :loading="loading">{{saveText}}</el-button>
          <el-button @click="goBack" v-if="showCencel">{{cencelText}}</el-button>
        </slot>
      </div>
    </div>
  </div>
</template>

<script>
export default {
  props: {
    title: {
      type: String,
      default: "",
    },
    saveText: {
      type: String,
      default: "保存",
    },
    cencelText: {
      type: String,
      default: "取消",
    },
    showCencel: {
      type: Boolean,
      default: true,
    },
    showSave: {
      type: Boolean,
      default: true,
    },
    loading: {
      type: Boolean,
      default: false,
    },
  },
  data() {
    return {
    };
  },
  methods: {
    goBack() {
      this.$router.go(-1);
      this.$emit('back')
    },
    saveHandle() {
      this.$emit('save')
    }
  },
};
</script>

<style lang="scss" scoped>
.page-wapper {
  width: 100%;
  height: 100%;
  padding: 16px;
  box-sizing: border-box;
  background: #EDF3FA;
}
.wapper-box {
  width: 100%;
  height: 100%;
  background-color: #fff;
  box-sizing: border-box;
  border-radius: 8px;
}

.page-detail-box {
  height: calc(100% - 112px);
  padding: 16px 24px;
  box-sizing: border-box;
  border-top: 1px solid #EBEBEB;
  border-bottom: 1px solid #EBEBEB;
  overflow: auto;
}
.page-detail-box-no-bottom {
  height: calc(100% - 56px);
}
.page-detail-bottom {
  height: 56px;
  display: flex;
  align-items: center;
  padding: 0 24px;
  box-sizing: border-box;
}
</style>
