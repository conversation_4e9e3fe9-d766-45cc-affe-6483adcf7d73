<template>
  <div class="tree-select-box">
    <el-tree-select
      v-model="selectValue"
      :data="treeData"
      :defaultValue="defaultValue"
      :node-key="nodeKey"
      @change="changeHandle"
      :props="treeProps"
      :load="loadNode"
      :lazy="lazy"
      :default-expanded-keys="defaultExpandedKeys"
      :disabled="disabled"
      :disabled-filter="disabledFilter"
      :multiple="multiple"
      :check-strictly="multiple"
      clearable
      @remove-tag="removeTag"
    >
      <template v-slot:default="scope">
        <span class="custom-tree-node">
          <div class="tree-node-label-box" :title="scope.node.label">
            <template v-if="isShowIcon">
              <span class="lable-org" v-if="scope.node.data.type === 'ORG'"
                >机构</span
              >
              <span
                class="lable-depart"
                v-else-if="scope.node.data.type === 'DEPT'"
                >部门</span
              >
            </template>
            <span
              class="node-label"
              :class="{ 'node-icon-label': isShowIcon }"
              >{{ scope.node.label }}</span
            >
          </div>
        </span>
      </template>
    </el-tree-select>
  </div>
</template>
<script>
import { requestApi } from "@/api/index";
import { treeSelectMixin } from '@/mixins/treeSelectMixin'
export default {
  mixins: [treeSelectMixin],
  data() {
    return {};
  },
  methods: {
    loadNode(node, resolve) {
      let autoParam = {
        ...this.otherParam,
      };
      this.autoParam.forEach((d) => {
        let params = d.split("=");
        autoParam[params[1]] =
          (node.data && node.data[params[0]]) ||
          this.otherParam[params[0]] ||
          "";
      });
      let params = {
        ...autoParam,
      };
      requestApi.postData(this.url, params).then((res) => {
        if (res.code === 200) {
          let data = res.data[this.listKey] || [];
          // 第一次加载时给默认值
          if (this.count === 0) {
            if (data.length > 0) {
              if (this.defaultExpandedKeys.length === 0) {
                this.defaultExpandedKeys = [data[0] && data[0][this.nodeKey]];
              }
            }
          }
          if (this.treeValue != "-1") {
            let tree = data.find((d) => d.id === this.treeValue);
            if (tree) {
              this.selectValue = "";
              setTimeout(() => {
                this.selectValue = this.treeValue;
              });
            }
          } else {
            this.selectValue = "";
          }
          this.count = this.count + 1;

          // 禁用组织机构选择
          if (
            this.disabledFilter ||
            (this.multiple && this.listKey == "departmentTree")
          ) {
            data = data.map((d) => {
              if (d.type === "ORG") {
                d.disabled = true;
              } else {
                d.disabled = false;
              }
              return d;
            });
          }
          // 过滤掉
          if(this.filterable && this.filterId) {
            data = data.filter(d => d.id!== this.filterId)
          }
          this.allTreeData.push(data);
          resolve(data);
        }
      });
    },
  },
};
</script>
<style lang="scss" scoped>
@import "./../common/DseTreeSelect.scss"
</style>

<style>
.tree-select-box .el-tree .is-disabled > .el-tree-node__content {
  cursor: not-allowed;
  color: #999999;
}
</style>