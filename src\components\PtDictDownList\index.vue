<!--字典项下拉列表控件-->

<template>
  <el-select
    v-if="elementType === 'select'"
    v-model="currentValue"
    ref="ddd"
    :disabled="disabled"
    @change="gradOptionsChanged"
    style="width: 100%"
  >
    <el-option
      v-for="item in dictArray"
      :key="item.value"
      :label="item.label"
      :value="item.value"
    >
    </el-option>
  </el-select>
  <el-radio-group
    v-else-if="elementType === 'radio'"
    v-model="currentValue"
    @change="gradOptionsChanged"
  >
    <el-radio v-for="item in dictArray" :key="item.value" :label="item.value">{{
      item.label
    }}</el-radio>
  </el-radio-group>
</template>

<script>
import { queryDictionary4Sel } from "@/api/common/commonData";

export default {
  name: "pt-dict-down-list",
  props: {
    elementType: {
      type: String,
      default: "select",
    },
    type: {
      type: String,
      default: "",
    },
    isRequired: {
      type: String,
      default: "",
    },
    label: {
      type: String,
      default: "",
    },
    disabled: {
      type: Boolean,
      default: false,
    },
    selectItem: {
      type: [String, Number],
      default() {
        return "";
      },
    },
    filterData: {
      //过滤的数据
      type: Array,
      default() {
        return [];
      },
    },
    addAll: {
      type: Boolean,
      default: false,
    },
    isDefault: {
      type: Boolean,
      default: true,
    },
  },

  watch: {
    selectItem: {
      handler(newData) {
        if (this.dictArray.length > 0) {
          if (newData) {
            this.currentValue = newData;
          } else {
            if (this.isDefault) {
              this.currentValue = this.dictArray[0].value;
              this.$emit("change", this.currentValue);
            }
          }
        }
      },
      immediate: true,
    },
  },
  data() {
    return {
      dictArray: [],
      dictArrayBase: [],
      currentValue: "",
      isFirstClick: true,
    };
  },
  methods: {
    gradOptionsChanged(data) {
      this.$emit("change", data);
    },
    handleFilterData() {
      if (this.filterData.length) {
        this.dictArray =
          this.dictArrayBase.filter(
            (i) => !this.filterData.includes(i.value)
          ) || [];
      } else {
        this.dictArray = this.dictArrayBase || [];
      }
      this.currentValue = (this.dictArray[0] && this.dictArray[0].value) || "";
      this.$emit("change", this.currentValue);
    },
  },
  mounted() {
    this.$nextTick(() => {
      queryDictionary4Sel(this.type, this.addAll)
        .then((dictData) => {
          this.dictArrayBase = dictData || [];
          this.dictArray = JSON.parse(JSON.stringify(this.dictArrayBase));
          if (this.addAll) {
            this.dictArray.unshift({
              value: "",
              label: "全部",
            });
          }
          this.currentValue = this.selectItem
            ? this.selectItem
            : this.isDefault ? this.dictArray[0].value : '';
          this.$emit("change", this.currentValue);
        })
        .catch((error) => {
          console.log(`获取字典项错误: ${error.message}`);
        });
    });
  },
};
</script>

<style scoped>
.el-scrollbar .el-scrollbar__view .el-select-dropdown__item {
  height: auto;
  max-height: 274px;
  padding: 0 15px;
  overflow: hidden;
  overflow-y: auto;
}

.el-select-dropdown__item.selected {
  font-weight: normal;
}

ul li >>> .el-tree .el-tree-node__content {
  height: auto;
  padding: 0 20px;
}

.el-tree-node__label {
  font-weight: normal;
}

.el-tree >>> .is-current .el-tree-node__label {
  color: #409eff;
  font-weight: 700;
}

.el-tree >>> .is-current .el-tree-node__children .el-tree-node__label {
  color: #606266;
  font-weight: normal;
}
</style>
