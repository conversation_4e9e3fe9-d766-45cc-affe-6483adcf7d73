<template>
  <div class="pt-nav-menu-tree-box">
    <div class="nav-title" v-if="title">{{ title }}</div>
    <div class="nav-search-box">
      <el-input
        placeholder="请输入内容"
        v-model="keyWord"
        class="input-with-select"
        @keyup.enter.native="searchHandle"
      >
        <el-button
          slot="append"
          icon="el-icon-search"
          @click="searchHandle"
        ></el-button>
      </el-input>
    </div>
    <div class="tree-box">
      <div class="tree-first-item" :class="{'tree-first-item-active': currentNodeKey === firstNode.id}" v-if="firstNode.name" @click="handleNodeClick(filterNode)">
        {{ firstNode.name }}
      </div>
      <el-tree
        ref="treeRef"
        v-if="treeShow"
        :node-key="nodeKey"
        :props="treeProps"
        :load="loadNode"
        :lazy="lazy"
        :data="treeData"
        :highlight-current="true"
        :default-expanded-keys="defaultExpandedKeys"
        :current-node-key="currentNodeKey"
        :filter-node-method="filterNode"
        :expand-on-click-node="false"
        :draggable="draggable"
        :allow-drag="allowDrag"
        :allow-drop="allowDrop"
        @node-click="handleNodeClick"
        @node-drag-start="handleNodeDragStart"
        @node-drop="handleDrop"
      >
        <span class="custom-tree-node" :class="{'custom-tree-node_dragging': dragging}" slot-scope="scope">
          <div class="tree-node-label-box" :title="scope.node.label">
            <span
              class="node-label"
              :class="{ 'node-icon-label': isShowIcon }"
              >{{ scope.node.label }}</span
            >
          </div>
          <template v-if="btnList.length > 0">
            <el-dropdown
              @command="($event) => handleCommand($event, scope.node)"
              v-if="
                (listKey === 'departmentTree' &&
                  scope.node.data.type === 'DEPT') ||
                listKey !== 'departmentTree'
              "
            >
              <span class="el-dropdown-link">
                <i class="el-icon-more"></i>
              </span>
              <el-dropdown-menu
                slot="dropdown"
                :append-to-body="false"
                class="dropdown-des-box"
              >
                <el-dropdown-item
                  v-for="item in btnList"
                  :key="item.code"
                  :command="item.code"
                >
                  <div class="drop-item">
                    <img
                      :src="
                        require('@/assets/images/icon/icon-' +
                          item.code +
                          '.svg')
                      "
                      class="drop-item-img drop-item-img-default"
                      alt=""
                      srcset=""
                    />
                    <img
                      :src="
                        require('@/assets/images/icon/icon-' +
                          item.code +
                          '-active.svg')
                      "
                      class="drop-item-img drop-item-img-active"
                      alt=""
                      srcset=""
                    />
                    <span>{{ item.name }}</span>
                  </div>
                </el-dropdown-item>
              </el-dropdown-menu>
            </el-dropdown>
          </template>
        </span>
      </el-tree>
    </div>
  </div>
</template>

<script>
import { requestApi } from "@/api/index";
import { menuTreeMixin } from "@/mixins/menuTreeMixin";
export default {
  mixins: [menuTreeMixin],
  data() {
    return {};
  },
  methods: {
    loadNode(node, resolve) {
      let autoParam = {
        ...this.otherParam,
      };
      this.autoParam.forEach((d) => {
        let params = d.split("=");
        autoParam[params[1]] =
          (node.data && node.data[params[0]]) ||
          this.otherParam[params[0]] ||
          "";
      });
      let params = {
        ...autoParam,
      };
      requestApi.postData(this.url, params).then((res) => {
        if (res.code === 200) {
          let data = res.data[this.listKey] || [];
          if (this.count === 0) {
            if (data.length > 0) {
              if (this.defaultExpandedKeys.length === 0) {
                this.defaultExpandedKeys = [data[0] && data[0][this.nodeKey]];
              }
              if(this.firstNode.name) {
                this.handleNodeClick(this.firstNode);
              } else {
                if(!this.currentNodeKey) {
                  this.handleNodeClick(data[0]);
                }
              } 
              
            } else {
              this.handleNodeClick(null)
            }
          }
          this.count = this.count + 1;
          if (this.listKey === "departmentTree") {
            // 处理组织机构部门树，组织和部门间没有pid做绑定的问题
            data = data.map((d) => {
              if (d.type === "DEPT" && d.pid === "-1") {
                d.pid = node.data.id;
              }
              return d;
            });
          }
          this.allTreeData.push(data);
          resolve(data);
        }
      });
    },
  },
};
</script>

<style lang="scss" scoped>
.pt-nav-menu-tree-box {
  width: 300px;
  height: 100%;
  margin-right: 16px;
  background: #fff;
  border-radius: 8px;
  flex-shrink: 0;
}

.nav-title {
  height: 48px;
  line-height: 48px;
  padding: 0 16px;
  box-sizing: border-box;
  color: #333;
  font-size: 16px;
  font-weight: bold;
}

.nav-search-box {
  padding: 0 16px;
  margin-bottom: 12px;
}

.tree-box {
  height: calc(100% - 92px);
  overflow-y: auto;
  box-sizing: border-box;
  padding: 0 2px 16px;
}
.tree-first-item {
  height: 40px;
  line-height: 40px;
  padding-left: 40px;
  cursor: pointer;
}
.tree-first-item:hover {
  background-color: #F9F9F9;
}
.tree-first-item-active {
  background-color: #E7F1FE;
}
.tree-first-item-active:hover {
  background-color: #E7F1FE;
}

/deep/ .el-tree {
  color: #333;
}

/deep/ .el-tree-node__content {
  height: 40px;
}

/deep/ .el-tree-node__label {
  padding: 0;
  height: 40px;
  line-height: 40px;
  box-sizing: border-box;
}

/deep/ .el-tree-node:not(.is-disabled) .el-tree-node__content .el-tree-node__label:hover,
/deep/ .el-tree-node:not(.is-disabled):focus>.el-tree-node__content>.el-tree-node__label {
  background: transparent;
}

/deep/ .el-tree-node:not(.is-disabled) .el-tree-node__content:hover {
  background: #f9f9f9;
}

/deep/ .el-tree-node:not(.is-disabled):focus>.el-tree-node__content {
  background: #e7f1fe;
}

/deep/ .el-tree--highlight-current .el-tree-node:not(.is-disabled).is-current>.el-tree-node__content>.el-tree-node__label {
  background: transparent;
}

/deep/ .el-tree--highlight-current .el-tree-node:not(.is-disabled).is-current>.el-tree-node__content {
  background: #e7f1fe;
}

/deep/ .el-tree-node__content>.el-tree-node__expand-icon {
  margin-left: 12px;
}

.custom-tree-node {
  display: flex;
  width: calc(100% - 40px);
  justify-content: space-between;
  align-items: center;
}
.icon-move {
  display: none;
  width: 16px;
  height: 16px;
  margin: 0 10px;
  position: relative;
  z-index: -1;
}

.el-dropdown-link {
  display: none;
  margin-right: 8px;
  width: 24px;
  height: 24px;
  background: #edf2f8;
  border-radius: 4px;
  cursor: pointer;
  line-height: 24px;
  text-align: center;
}

.el-icon-more {
  transform: rotate(90deg);
  color: #0d70f2;
}

/deep/ .el-tree-node__content:hover .el-dropdown-link {
  display: inline-block;
}
/deep/ .el-tree-node__content:hover .icon-move {
  display: inline-block;
}

/deep/ .el-tree-node>.el-tree-node__children {
  overflow: visible;
}

.tree-node-label-box {
  width: calc(100% - 38px);
  display: flex;
  align-items: center;
}

.node-label {
  display: inline-block;
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
}

.node-icon-label {
  width: calc(100% - 38px);
}

.lable-org {
  display: inline-block;
  background: rgba(13,112,242,0.10);
  color: #0d70f2;
  border-radius: 2px;
  margin-right: 4px;
  font-size: 12px;
  padding: 5px;
}

.lable-depart {
  display: inline-block;
  background: rgba(41,204,204, 0.10);
  color: #29cccc;
  border-radius: 2px;
  margin-right: 4px;
  font-size: 12px;
  padding: 5px;
}


.dropdown-des-box {
  padding: 10px 0;
}

.dropdown-des-box .el-dropdown-menu__item {
  padding: 0 20px;
}

.drop-item-img {
  width: 14px;
  height: 14px;
  margin-right: 8px;
  vertical-align: middle;
}

.drop-item-img-active {
  display: none;
}

.drop-item-img-default {
  display: inline-block;
}

.drop-item:hover .drop-item-img-default {
  display: none;
}

.drop-item:hover .drop-item-img-active {
  display: inline-block;
}
</style>
