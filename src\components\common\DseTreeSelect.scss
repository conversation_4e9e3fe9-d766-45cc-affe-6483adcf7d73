.custom-tree-node {
  display: flex;
  width: calc(100% - 40px);
  justify-content: space-between;
  align-items: center;
}

.tree-node-label-box {
  width: calc(100% - 38px);
  display: flex;
  align-items: center;
}

.lable-org {
  display: inline-block;
  background: rgba(13,112,242,0.10);
  color: #0d70f2;
  border-radius: 2px;
  margin-right: 4px;
  font-size: 10px;
  padding: 3px;
}

.lable-depart {
  display: inline-block;
  background: rgba(41,204,204, 0.10);;
  color: #29cccc;
  border-radius: 2px;
  margin-right: 4px;
  font-size: 10px;
  padding: 3px;
}

.node-label {
  display: inline-block;
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
  text-align: left;
}

.node-icon-label {
  width: calc(100% - 38px);
}
