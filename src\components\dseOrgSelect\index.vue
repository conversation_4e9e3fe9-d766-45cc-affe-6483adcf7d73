<template>
  <el-cascader
    :props="treeOrgProps"
    :options="treeData"
    v-model="orgId"
    style="width: 100%"
    clearable
    :show-all-levels="false"
    @change="selectChanges"
  ></el-cascader>
</template>
<script>
import { getOrgTreeRequest } from "@/api/baseInfo/baseInfoData";
export default {
  name: "dseOrgSelect",
  props: {
    value: {
      type: Array,
      default: () => [],
    },
  },
  watch: {
    value: {
      handler(newVal) {
        this.orgId = newVal;
      },
    },
  },
  data() {
    let that = this;
    return {
      orgId: [],
      treeData: [],
      treeOrgProps: {
        label: "name",
        children: "childsNode",
        value: "id",
        checkStrictly: true,
        lazy: false,
        lazyLoad(node, resolve) {
          that.getOrgData(node, resolve);
        },
      },
    };
  },
  mounted() {
    this.getData();
  },
  methods: {
    getData() {
      getOrgTreeRequest({
        isSync: false,
        pid: "",
      }).then((res) => {
        if (res.code === 200) {
          this.treeData = res.data.organizationTree || [];
        }
      });
    },
    getOrgData(node, resolve) {
      getOrgTreeRequest({
        isSync: true,
        pid: node.value || "",
      }).then((res) => {
        let data = res.data.organizationTree || [];
        resolve(data);
      });
    },
    selectChanges(value) {
      this.$emit("changes", value);
    },
  },
};
</script>