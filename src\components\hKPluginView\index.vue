<template>
  <div class="hk-plugin-view" :ref="'haiKangPlugin' + keyIndex">
    <div
      :ref="'playWnd' + keyIndex"
      class="playWnd"
      :id="'playWnd' + keyIndex"
    ></div>
  </div>
</template>


<script>
import { getSecureConfByCodeRequest } from "@/api/play/playData";
import dayjs from "dayjs";
export default {
  name: "HaiKangPluginView",
  data() {
    return {
      searverConfig: window.DSE.haiKangServiceConfig,
      mainStreamReqWinNum:
        window.DSE?.haiKangServiceConfig?.mainStreamReqWinNum || 1,
      oWebControl: null,
      initCount: 0, // 插件初始化次数
      pubKey: "",
      width: 0,
      height: 0,
      isInit: 0, // 是否初始化
      currentIndex: 0, // 当前播放的通道
      wndNum: 4,
      currentRealList: [],
    };
  },
  props: {
    cameraIndexCode: {
      type: String,
      default: "",
    },
    viewType: {
      type: String,
      default: "preview",
    },
    keyIndex: {
      type: Number,
      default: 0,
    },
  },
  watch: {
    cameraIndexCode: {
      handler() {
        if (this.cameraIndexCode) {
          // if (!this.isInit) {
          //   this.getConfigHandle();
          // } else {
          //   this.startPreview();
          // }
        }
      },
      immediate: true,
      deep: true,
    },
  },
  mounted() {
    let that = this;
    this.$nextTick(() => {
      const resizeObserver = new ResizeObserver((entries) => {
        for (let entry of entries) {
          const { width, height } = entry.contentRect;
          that.width = width;
          that.height = height;
          console.log("resizeObserver", that.width, that.height);
          that.resizeHandle();
        }
        that.$forceUpdate();
      });
      resizeObserver.observe(this.$refs["haiKangPlugin" + that.keyIndex]);
    });
    this.getConfigHandle();
  },
  beforeDestroy() {
    if (this.oWebControl) {
      this.oWebControl.JS_HideWnd();
      this.oWebControl.JS_DestroyWnd().then(() => {
        this.oWebControl = null;
      });
    }
  },
  methods: {
    getConfigHandle() {
      getSecureConfByCodeRequest().then((res) => {
        if (res.code === 200) {
          this.searverConfig = res.data;
          this.initPlugin();
        }
      });
    },
    resetHandle() {
      if (this.oWebControl) {
        this.oWebControl.JS_HideWnd();
        this.oWebControl = null;
        this.initPlugin();
      }
    },
    initPlugin() {
      let that = this;
      this.oWebControl = new WebControl({
        szPluginContainer: "playWnd" + that.keyIndex, // 指定容器id
        iServicePortStart: 15900, // 指定起止端口号，建议使用该值
        iServicePortEnd: 15900,
        szClassId: "23BF3B0A-2C56-4D97-9C03-0CB103AA8F11", // 用于IE10使用ActiveX的clsid
        cbConnectSuccess: function () {
          // 创建WebControl实例成功
          that.oWebControl
            .JS_StartService("window", {
              // WebControl实例创建成功后需要启动服务
              dllPath: "./VideoPluginConnect.dll", // 值"./VideoPluginConnect.dll"写死
            })
            .then(
              function () {
                // 启动插件服务成功
                console.log("启动插件服务成功");
                that.oWebControl.JS_SetWindowControlCallback({
                  // 设置消息回调
                  cbIntegrationCallBack: that.cbIntegrationCallBack,
                });

                that.oWebControl
                  .JS_CreateWnd(
                    "playWnd" + that.keyIndex,
                    that.width,
                    that.height,
                    {
                      bEmbed: true,
                    }
                  )
                  .then(function () {
                    //JS_CreateWnd创建视频播放窗口，宽高可设定
                    that.init(); // 创建播放实例成功后初始化
                  });
              },
              function () {
                // 启动插件服务失败
              }
            );
        },
        cbConnectError: function () {
          // 创建WebControl实例失败
          this.oWebControl = null;
          // $("#playWnd").html("插件未启动，正在尝试启动，请稍候...");
          that.$message.error("插件未启动，正在尝试启动，请稍候...");
          WebControl.JS_WakeUp("VideoWebPlugin://"); // 程序未启动时执行error函数，采用wakeup来启动程序
          that.initCount++;
          if (that.initCount < 3) {
            setTimeout(function () {
              that.initPlugin();
            }, 3000);
          } else {
            // $("#playWnd").html("插件启动失败，请检查插件是否安装！");
            // that.$message.error("插件启动失败，请检查插件是否安装！");
            that.confirmPluginInstall();
          }
        },
        cbConnectClose: function (bNormalClose) {
          // 异常断开：bNormalClose = false
          // JS_Disconnect正常断开：bNormalClose = true
          console.log("cbConnectClose");
          that.oWebControl = null;
          // $("#playWnd").html("插件未启动，正在尝试启动，请稍候...");
          that.$message.error("插件未启动，正在尝试启动，请稍候...");
          WebControl.JS_WakeUp("VideoWebPlugin://");
          that.initCount++;
          if (that.initCount < 3) {
            setTimeout(function () {
              that.initPlugin();
            }, 3000);
          } else {
            // $("#playWnd").html("插件启动失败，请检查插件是否安装！");
            // that.$message.error("插件启动失败，请检查插件是否安装！");
            that.confirmPluginInstall();
          }
        },
      });
    },
    confirmPluginInstall() {
      let that = this;
      this.$emit("isNotInstall");
      this.$confirm("插件启动失败，请检查插件是否安装？", "提示", {
        confirmButtonText: "已安装",
        cancelButtonText: "下载插件",
        type: "warning",
      })
        .then(() => {
          // window.location.href = "/static/haiKangPlugin/VideoWebPlugin.exe";
          that.initPlugin();
          that.initCount = 0;
        })
        .catch(() => {
          window.location.href = window.DSE.haiKangPluginDownUrl;
        });
    },
    cbIntegrationCallBack(oData) {
      let type = oData.responseMsg.type;
      let cameraIndexCode = "";
      let that = this;
      // console.log(9000, oData);
      switch (type) {
        case 1:
          cameraIndexCode =
            (oData.responseMsg.msg && oData.responseMsg.msg.cameraIndexCode) ||
            "";
          that.currentIndex =
            (oData.responseMsg.msg && oData.responseMsg.msg.wndId) || 0;
          that.$emit("selectCamera", cameraIndexCode);
          //鼠标选中当前窗口
          // this.currentVideo = JSON.parse(oData.responseMsg.msg);
          // this.currentVideo = oData.responseMsg.msg;
          break;
        case 2:
          cameraIndexCode =
            (oData.responseMsg.msg && oData.responseMsg.msg.cameraIndexCode) ||
            "";
          // that.$emit("playOrStop", oData.responseMsg.msg);
          that.setRealList(oData.responseMsg.msg);
          break;
        case 5:
          //全屏
          break;
        case 6:
          // 切换布局
          let data = oData.responseMsg.msg;
          that.wndNum = data.wndNum;
          that.toggleLayout(data);
          break;
        case 7:
          // 双击窗口，wndAction：1是全屏，0是退出全屏
          const itemNode = oData.responseMsg.msg;
          // const { cameraIndexCode, wndId, wndAction } = oData.responseMsg.msg;
          if (itemNode.wndAction === 1) {
            that.realPreview(itemNode.cameraIndexCode, 0, itemNode.wndId);
          } else {
            const streamMode = that.getStreamMode();
            that.realPreview(
              itemNode.cameraIndexCode,
              streamMode,
              itemNode.wndId
            );
          }
          break;
      }
    },
    setRealList(data) {
      // result :768开始播放，816停止播放
      console.log("播放或者停止播放", data);
      if (this.viewType === "preview") {
        const itemIndex = this.currentRealList.findIndex(
          (item) => item.wndId === data.wndId
        );
        if (data.result === 768) {
          if (itemIndex > -1) {
            this.currentRealList[itemIndex].cameraIndexCode =
              data.cameraIndexCode;
          } else {
            this.currentRealList.push({
              cameraIndexCode: data.cameraIndexCode,
              wndId: data.wndId,
            });
          }
        } else {
          this.currentRealList = this.currentRealList.filter(
            (item) => item !== data.cameraIndexCode
          );
        }
      }
    },
    toggleLayout(data) {
      if (this.viewType === "preview") {
        const item = this.currentRealList.find(
          (item) => item.wndId === 1
        );
        if (item) {
          if(data.wndNum === 1) {
            this.realPreview(item.cameraIndexCode, 0, 1);
          } else {
            this.realPreview(item.cameraIndexCode, 1, 1);
          }
        }
      }
    },
    init() {
      console.log("init");
      let that = this;
      let playMode = 0; //初始播放模式：0-预览，1-回放
      let showToolbar = 0; //是否显示工具栏，0-不显示，非0-显示
      let layout = "2x2";
      if (that.viewType === "preview") {
        playMode = 0;
        showToolbar = 1;
        layout = "";
        that.wndNum = 4;
      } else {
        playMode = 1;
        showToolbar = 1;
        layout = "1x1";
        that.wndNum = 1;
      }
      that.getPubKey(function () {
        console.log("searverConfig", that.searverConfig);
        ////////////////////////////////// 请自行修改以下变量值	////////////////////////////////////
        var appkey = that.searverConfig.appKey; //综合安防管理平台提供的appkey，必填
        var secret = that.setEncrypt(that.searverConfig.appSecret); //综合安防管理平台提供的secret，必填
        var ip = that.searverConfig.ip; //综合安防管理平台IP地址，必填
        // var playMode = 0; //初始播放模式：0-预览，1-回放
        var port = Number(that.searverConfig.host); //综合安防管理平台端口，若启用HTTPS协议，默认443
        var snapDir = "D:\\SnapDir"; //抓图存储路径
        var videoDir = "D:\\VideoDir"; //紧急录像或录像剪辑存储路径
        // var layout = "2x2"; //playMode指定模式的布局
        var enableHTTPS = 1; //是否启用HTTPS协议与综合安防管理平台交互，这里总是填1
        var encryptedFields = "secret"; //加密字段，默认加密领域为secret
        // var showToolbar = ; //是否显示工具栏，0-不显示，非0-显示
        var showSmart = 1; //是否显示智能信息（如配置移动侦测后画面上的线框），0-不显示，非0-显示
        var buttonIDs =
          "16,256,257,258,259,260,512,513,514,515,516,517,768,769"; //自定义工具条按钮
        ////////////////////////////////// 请自行修改以上变量值	////////////////////////////////////
        that.oWebControl
          .JS_RequestInterface({
            funcName: "init",
            argument: JSON.stringify({
              appkey: appkey, //API网关提供的appkey
              secret: secret, //API网关提供的secret
              ip: ip, //API网关IP地址
              playMode: playMode, //播放模式（决定显示预览还是回放界面）
              port: port, //端口
              snapDir: snapDir, //抓图存储路径
              videoDir: videoDir, //紧急录像或录像剪辑存储路径
              layout: layout, //布局
              enableHTTPS: enableHTTPS, //是否启用HTTPS协议
              encryptedFields: encryptedFields, //加密字段
              showToolbar: showToolbar, //是否显示工具栏
              showSmart: showSmart, //是否显示智能信息
              buttonIDs: buttonIDs, //自定义工具条按钮
            }),
          })
          .then(function (oData) {
            that.resizeHandle(); // 初始化后resize一次，规避firefox下首次显示窗口后插件窗口未与DIV窗口重合问题
          });
      });
    },
    setEncrypt(value) {
      var encrypt = new JSEncrypt();
      encrypt.setPublicKey(this.pubKey);
      return encrypt.encrypt(value);
    },

    //获取公钥
    getPubKey(callback) {
      let that = this;
      that.oWebControl
        .JS_RequestInterface({
          funcName: "getRSAPubKey",
          argument: JSON.stringify({
            keyLength: 1024,
          }),
        })
        .then(function (oData) {
          console.log("getPubKey", oData, that.initCount);
          if (oData.responseMsg.data) {
            that.pubKey = oData.responseMsg.data;
            callback();
          }
        });
    },
    getStreamMode() {
      console.log(333333, this.wndNum, this.mainStreamReqWinNum);
      let that = this;
      if (that.wndNum > that.mainStreamReqWinNum) {
        return 1;
      } else {
        return 0;
      }
    },
    async startPreview(code) {
      let that = this;
      let streamMode = await this.getStreamMode();
      that.realPreview(code, streamMode, -1);
    },
    realPreview(code, streamMode, wndId) {
      let that = this;
      var cameraIndexCode = code; //获取输入的监控点编号值，必填
      // var streamMode = 1; //主子码流标识：0-主码流，1-子码流
      var transMode = 1; //传输协议：0-UDP，1-TCP
      var gpuMode = 0; //是否启用GPU硬解，0-不启用，1-启用
      // var wndId = that.currentIndex; //播放窗口序号（在2x2以上布局下可指定播放窗口）
      cameraIndexCode = cameraIndexCode.replace(/(^\s*)/g, "");
      cameraIndexCode = cameraIndexCode.replace(/(\s*$)/g, "");

      // streamMode = await this.getStreamMode();
      console.log(789999, streamMode);
      that.oWebControl
        .JS_RequestInterface({
          funcName: "startPreview",
          argument: JSON.stringify({
            cameraIndexCode: cameraIndexCode, //监控点编号
            streamMode: streamMode, //主子码流标识
            transMode: transMode, //传输协议
            gpuMode: gpuMode, //是否开启GPU硬解
            wndId: wndId, //可指定播放窗口
          }),
        })
        .then(function (oData) {
          console.log("startPreview", oData);
        });
    },
    async batchStartPreview(list) {
      let that = this;
      let streamMode = this.getStreamMode();
      that.oWebControl
        .JS_RequestInterface({
          funcName: "getLayout",
        })
        .then(function (oData) {
          // let data = JSON.parse(oData.responseMsg.data);
          var Data = JSON.stringify(oData.responseMsg.data);
          Data = Data.replace(/\\n/g, "");
          Data = Data.replace(/\\/g, "");
          Data = Data.replace(/\"{/g, "{");
          Data = Data.replace(/}\"/g, "}");
          let WndNum = JSON.parse(Data).wndNum;
          let minLength = Math.min(WndNum, list.length);
          let arguList = [];
          for (let i = 0; i < minLength; i++) {
            arguList.push({
              cameraIndexCode: list[i] ? list[i].cameraIndexCode : "", //监控点编号
              ezvizDirect: 0, //是否启用直连萤石云，0-不启用，1-启用
              streamMode: streamMode, //主子码流标识：0-主码流，1-子码流
              transMode: 1, //传输协议：0-UDP，1-TCP
              gpuMode: 0, //是否启用GPU硬解，0-不启用，1-启用
              wndId: i + 1, //播放窗口序号（在2x2以上布局下可指定播放窗口）
            });
          }
          that.oWebControl
            .JS_RequestInterface({
              funcName: "startMultiPreviewByCameraIndexCode",
              argument: JSON.stringify({ list: arguList }),
            })
            .then(function (oData) {
              console.log("startMultiPreviewByCameraIndexCode", oData);
            });
        });
    },
    playBackHandle(code, start, end, recordLocation) {
      // if(!start || !end) {
      //   return
      // }
      let playTime = dayjs().format("YYYY-MM-DD");
      playTime = playTime + " 00:00:00";
      let startTime = "";
      let endTime = "";
      if (start) {
        startTime = dayjs(start).format("YYYY-MM-DD HH:mm:ss");
      } else {
        startTime = dayjs().format("YYYY-MM-DD");
        startTime = startTime + " 00:00:00";
      }
      if (end) {
        endTime = dayjs(end).format("YYYY-MM-DD HH:mm:ss");
      } else {
        endTime = dayjs().format("YYYY-MM-DD HH:mm:ss");
      }
      var cameraIndexCode = code; //获取输入的监控点编号值，必填
      var startTimeStamp = new Date(
        startTime.replace("-", "/").replace("-", "/")
      ).getTime(); //回放开始时间戳，必填
      var endTimeStamp = new Date(
        endTime.replace("-", "/").replace("-", "/")
      ).getTime(); //回放结束时间戳，必填
      // var recordLocation = recordLocation || 0; //录像存储位置：0-中心存储，1-设备存储
      var transMode = 1; //传输协议：0-UDP，1-TCP
      var gpuMode = 0; //是否启用GPU硬解，0-不启用，1-启用
      var wndId = 1; //播放窗口序号（在2x2以上布局下可指定播放窗口）
      var playTimeStamp = new Date(
        playTime.replace("-", "/").replace("-", "/")
      ).getTime(); //指定播放时间戳，0-不指定

      console.log(
        99999,
        JSON.stringify({
          cameraIndexCode: cameraIndexCode, //监控点编号
          startTimeStamp: Math.floor(startTimeStamp / 1000).toString(), //录像查询开始时间戳，单位：秒
          endTimeStamp: Math.floor(endTimeStamp / 1000).toString(), //录像结束开始时间戳，单位：秒
          recordLocation: Number(recordLocation), //录像存储类型：0-中心存储，1-设备存储
          transMode: transMode, //传输协议：0-UDP，1-TCP
          gpuMode: gpuMode, //是否启用GPU硬解，0-不启用，1-启用
          wndId: wndId, //可指定播放窗口
        }),
        startTime,
        endTime
      );
      this.oWebControl.JS_RequestInterface({
        funcName: "startPlayback",
        argument: JSON.stringify({
          cameraIndexCode: cameraIndexCode, //监控点编号
          startTimeStamp: Math.floor(startTimeStamp / 1000).toString(), //录像查询开始时间戳，单位：秒
          endTimeStamp: Math.floor(endTimeStamp / 1000).toString(), //录像结束开始时间戳，单位：秒
          recordLocation: Number(recordLocation), //录像存储类型：0-中心存储，1-设备存储
          transMode: transMode, //传输协议：0-UDP，1-TCP
          gpuMode: gpuMode, //是否启用GPU硬解，0-不启用，1-启用
          wndId: wndId, //可指定播放窗口
        }),
      });
    },
    resizeHandle() {
      console.log("resizeHandle", this.width, this.height);
      if (this.oWebControl) {
        this.setDocOffset();
        // this.oWebControl.JS_Resize(this.width, this.height);
        this.setWndCover();
      }
    },
    setDocOffset() {
      var iframeRect = window.frameElement;
      var oIframeRect = {
        left: 0,
        top: 0,
      };
      if (iframeRect) {
        oIframeRect = iframeRect.getBoundingClientRect();
        console.log("iframeRect----", oIframeRect);
      }
      this.oWebControl.JS_SetDocOffset({
        left: oIframeRect.left,
        top: oIframeRect.top,
      }); // 更新插件窗口位置
      this.oWebControl.JS_Resize(this.width, this.height);
      this.setWndCover();
    },
    setWndCover() {
      // // 准备要用到的一些数据
      var iWidth = $(window).width();
      var iHeight = $(window).height();
      var oDivRect = $("#playWnd" + this.keyIndex)
        .get(0)
        .getBoundingClientRect();

      var iframeRect = window.frameElement;
      var oIframeRect = {
        left: 0,
        top: 0,
      };
      if (iframeRect) {
        oIframeRect = iframeRect.getBoundingClientRect();
        console.log("iframeRect----", oIframeRect);
      }

      var iCoverLeft = oDivRect.left < 0 ? Math.abs(oDivRect.left) : 0;
      var iCoverTop = oDivRect.top < 0 ? Math.abs(oDivRect.top) : 0;

      var iCoverRight =
        oDivRect.right - iWidth > 0 ? Math.round(oDivRect.right - iWidth) : 0;

      var iCoverBottom =
        oDivRect.bottom - iHeight > 0
          ? Math.round(oDivRect.bottom - iHeight)
          : 0;

      iCoverLeft = iCoverLeft > this.width ? this.width : iCoverLeft;
      iCoverTop = iCoverTop > this.height ? this.height : iCoverTop;
      iCoverRight = iCoverRight > this.width ? this.width : iCoverRight;
      iCoverBottom = iCoverBottom > this.height ? this.height : iCoverBottom;

      this.oWebControl.JS_RepairPartWindow(0, 0, this.width + 1, this.height); // 多1个像素点防止还原后边界缺失一个像素条
      if (iCoverLeft != 0 && oIframeRect.left != 0) {
        this.oWebControl.JS_CuttingPartWindow(0, 0, iCoverLeft, this.height);
      }
      if (iCoverTop != 0) {
        this.oWebControl.JS_CuttingPartWindow(0, 0, this.height + 1, iCoverTop); // 多剪掉一个像素条，防止出现剪掉一部分窗口后出现一个像素条
      }
      if (iCoverRight != 0) {
        this.oWebControl.JS_CuttingPartWindow(
          this.width - iCoverRight,
          0,
          iCoverRight,
          this.height
        );
      }
      if (iCoverBottom != 0) {
        this.oWebControl.JS_CuttingPartWindow(
          0,
          this.height - iCoverBottom,
          this.width,
          iCoverBottom
        );
      }
    },
  },
};
</script>
<style lang="scss" scoped>
.hk-plugin-view {
  width: 100%;
  height: 100%;
  .playWnd {
    width: 100%;
    height: 100%;
  }
}
</style>