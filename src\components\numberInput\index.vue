<template>
    <el-input
      :value="currentValue"
      :placeholder="placeholder"
      @input="handleInput"
      @blur="handleBlur"
      :style="{ width: width }"
      :disabled="disabled"
    ></el-input>
  </template>
  
  <script>
  export default {
    name: 'NumberInput',
    props: {
      // v-model 绑定值
      value: {
        type: [String, Number, Object],
        default: ''
      },
      // 最小值
      min: {
        type: Number,
        default: 0
      },
      // 最大值
      max: {
        type: Number,
        default: 1
      },
      // 小数位数
      decimal: {
        type: Number,
        default: 3
      },
      // 占位符
      placeholder: {
        type: String,
        default: '请输入数字'
      },
      // 宽度
      width: {
        type: String,
        default: '100%'
      },
      // 是否禁用
      disabled: {
        type: Boolean,
        default: false
      }
    },
  
    data() {
      return {
        currentValue: ''
      }
    },
  
    watch: {
      value: {
        handler(newVal) {
          this.currentValue = newVal
        },
        immediate: true
      }
    },
  
    methods: {
      handleInput(value) {
        // 只允许输入数字和小数点
        value = value.replace(/[^\d.]/g, '')
        // 只允许一个小数点
        value = value.replace(/\.{2,}/g, '.')
        // 限制小数位数
        value = value.replace(new RegExp(`^(\\d+)\\.(\\d{${this.decimal}}).*$`), '$1.$2')
        
        // 确保值在min-max之间
        const numValue = parseFloat(value)
        if (!isNaN(numValue)) {
          if (numValue > this.max) value = this.max
          if (numValue < this.min) value = this.min
        }
        
        this.currentValue = value
        this.$emit('input', value)
        this.$emit('change', value)
      },
      
      handleBlur(event) {
        let value = event.target.value
        // 格式化为指定小数位数
        if (value) {
          value = parseFloat(value).toFixed(this.decimal)
          this.currentValue = value
          this.$emit('input', value)
          this.$emit('change', value)
        }
      }
    }
  }
  </script>