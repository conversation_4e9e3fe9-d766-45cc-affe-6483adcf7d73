<template>
  <div class="polygon-draw" ref="container"></div>
</template>
      
      <script>
import Konva from "konva";

export default {
  props: {
    areaPoints: {
      type: Array,
      default() {
        return [];
      },
    },
  },
  watch: {
    areaPoints: {
      handler() {
        if (this.areaPoints && this.areaPoints.length > 0) {
          this.initArea();
        }
      },
      immediate: true,
      deep: true,
    },
  },
  data() {
    return {
      stage: null, // 存储Konva舞台
      layer: null, // 存储Konva图层
      currentPoints: [], // 当前正在绘制的平行四边形的点
      shapes: [], // 存储所有平行四边形的数据
      line: null,
      isDrawing: false,
      width: 0,
      height: 0,
      isDragging: false,
      currentGatage: -1
    };
  },
  mounted() {
    // 初始化Konva舞台
    this.initKonva();
    window.addEventListener('resize', this.handleResize);
  },
  beforeDestroy() {
    // 移除监听器
    window.removeEventListener('resize', this.handleResize);
  },

  methods: {
    handleResize() {
      const container = this.$refs.container;
      const { width, height } = container.getBoundingClientRect();
      
      // 更新舞台大小
      this.width = width;
      this.height = height;
      this.stage.width(width);
      this.stage.height(height);
      this.layer.destroyChildren();
      // 重新绘制所有图形
      this.initArea();
    },
    initKonva() {
      const container = this.$refs.container;
      const { width, height } = container.getBoundingClientRect();
      this.width = width;
      this.height = height;
      // 创建Konva舞台
      this.stage = new Konva.Stage({
        container: container,
        width: width,
        height: height,
      });

      // 创建图层
      this.layer = new Konva.Layer();
      this.stage.add(this.layer);

      // 设置监听事件
      this.stage.on("mousedown", this.handleMouseDown);
      this.stage.on("mousemove", this.handleMouseMove);
      this.stage.on("contextmenu", this.handleRightClick);
      if (this.areaPoints) {
        this.initArea();
      }
    },
    initArea() {
      this.shapes = [];
      if (
        this.areaPoints &&
        Array.isArray(this.areaPoints) &&
        this.areaPoints.length > 0
      ) {
        // 清除之前的绘制
        this.layer && this.layer.destroyChildren();

        // 遍历每个平行四边形数据
        this.areaPoints.forEach((areaPoint, index) => {
          const points = areaPoint.points.map((d) => ({
            x: (d[0] / areaPoint.width) * this.width,
            y: (d[1] / areaPoint.height) * this.height,
          }));

          // 创建闭合的多边形
          const polygon = new Konva.Line({
            points: points.flatMap((p) => [p.x, p.y]),
            fill: "rgba(255,255,255, 0.2)",
            stroke: "#FF0303",
            strokeWidth: 1,
            closed: true,
            lineJoin: "round",
          });

          // 计算平行四边形的中心点
          const centerX = points.reduce((sum, p) => sum + p.x, 0) / 4;
          const centerY = points.reduce((sum, p) => sum + p.y, 0) / 4;

          // 创建文本标签
          const label = this.drawText(centerX, centerY, `${index + 1}#闸门`);

          // 为每个点创建圆点标记
          points.forEach((point) => {
            const circle = this.drawPoint(point.x, point.y);
            this.layer.add(circle);
          });

          // 将多边形和标签添加到图层
          this.layer.add(polygon);
          this.layer.add(label);

          // 保存到shapes数组中
          this.shapes.push({
            points: points,
            shape: polygon,
          });
        });

        // 重绘图层
        this.layer.batchDraw();
      }
    },
    startDraw(value) {
      this.isDrawing = true;
      this.currentPoints = [];
      this.currentGatage = value;
    },
    getPointerPosition(value, point) {
      const { w, h } = this.areaPoints;
      if (point === "x") {
        return (value / w) * this.width;
      } else {
        return (value / h) * this.height;
      }
    },
    handleMouseDown(event) {
      console.log(
        "handleMouseDown",
        this.currentPoints,
        this.isDrawing,
        this.isDragging,

        this.currentPoints.length >= 3 ||
          (this.currentPoints.length > 0 && !this.isDrawing) ||
          this.isDragging
      );
      if (
        this.currentPoints.length >= 3 ||
        (this.currentPoints.length > 0 && !this.isDrawing) ||
        this.isDragging
      ) {
        return;
      }
      // 获取鼠标点击位置
      const mousePos = this.stage.getPointerPosition();
      this.currentPoints.push(mousePos);
      this.isDrawing = true;

      // 在画布上绘制点标记
      const point = this.drawPoint(mousePos.x, mousePos.y);
      this.layer.add(point);

      // 如果是第三个点，自动计算第四个点并完成绘制
      if (this.currentPoints.length === 3) {
        const fourthPoint = this.calculateFourthPoint();
        this.currentPoints.push(fourthPoint);

        // 创建闭合的平行四边形
        const polygon = this.drawLine();
        this.layer.add(polygon);

        const fourthPointCrical = this.drawPoint(fourthPoint.x, fourthPoint.y);
        this.layer.add(fourthPointCrical);

        // 保存当前平行四边形的数据
        this.shapes[this.currentGatage] = {
          points: [...this.currentPoints],
          shape: polygon,
        };

        // // 重置当前点集，准备绘制下一个
        // this.currentPoints = [];
        // // this.isDrawing = false;
        this.setData();
      }

      this.layer.batchDraw();
    },
    // 添加计算第四个点的方法
    calculateFourthPoint() {
      const [p1, p2, p3] = this.currentPoints;
      return {
        x: p1.x + (p3.x - p2.x),
        y: p1.y + (p3.y - p2.y),
      };
    },
    handleMouseMove(event) {
      if (!this.isDrawing || this.currentPoints.length >= 3 || this.isDragging)
        return;
      const mousePos = this.stage.getPointerPosition();

      // 清除之前的预览线
      if (this.line) {
        this.line.destroy();
      }

      // 重新绘制当前正在绘制的平行四边形的点标记
      this.layer.children.forEach((node) => {
        if (node.attrs.temp === true) {
          // 添加临时标记以识别当前绘制的点
          node.destroy();
        }
      });

      this.currentPoints.forEach((point) => {
        const circle = this.drawPoint(point.x, point.y);
        circle.attrs.temp = true; // 标记为临时点
        this.layer.add(circle);
      });

      // 绘制预览线
      const line = new Konva.Line({
        points: this.getLinePoints(mousePos),
        stroke: "#FF0303",
        strokeWidth: 1,
        temp: true,
      });
      this.layer.add(line);
      this.line = line;

      this.layer.batchDraw();
    },

    handleRightClick(event) {
      event.evt.preventDefault();
      if (!this.isDrawing) return;
      // 如果点位数量大于2个，则闭合多边形并填充
      if (this.currentPoints.length > 2) {
        const mousePos = this.stage.getPointerPosition();
        this.currentPoints.push(mousePos);

        // 创建闭合的多边形
        const polygon = this.drawLine();

        this.layer.add(polygon);
        this.layer.batchDraw();

        // 重置点和实时线条
        //   this.points = [];
        //   this.line = null;
        this.isDrawing = false;
        this.setData();
      }
    },

    getLinePoints(mousePos = null) {
      const points = this.currentPoints.flatMap((point) => [point.x, point.y]);
      if (mousePos) {
        points.push(mousePos.x, mousePos.y);
      }
      return points;
    },
    drawPoint(x, y) {
      const point = new Konva.Circle({
        x: x,
        y: y,
        radius: 4,
        fill: "#0000FF",
        // stroke: "black",
        // strokeWidth: 2,
      });
      return point;
    },
    drawText(x, y, str) {
      const text = new Konva.Text({
        x: x - 20, // 调整文本位置，使其居中
        y: y - 8, // 调整文本位置，使其居中
        text: str,
        fontSize: 14,
        fontFamily: "Arial",
        fill: "#FF0303",
        fontStyle: "bold",
        align: "center",
      });
      return text;
    },
    drawLine(mousePos) {
      const polygon = new Konva.Line({
        points: this.getLinePoints(mousePos),
        fill: "rgba(255,255,255, 0.2)",
        stroke: "#FF0303",
        strokeWidth: 1,
        closed: true,
        lineJoin: "round",
        // draggable: true,
      });
      // 计算平行四边形的中心点
      const points = this.getLinePoints(mousePos);
      const centerX = (points[0] + points[2] + points[4] + points[6]) / 4;
      const centerY = (points[1] + points[3] + points[5] + points[7]) / 4;

      // 创建文本标签
      const label = this.drawText(
        centerX,
        centerY,
        `${this.currentGatage + 1}#闸门`
      );
      // 将文本添加到图层
      this.layer.add(label);

      // // 为每个形状添加拖拽事件
      // polygon.on("dragstart", () => {
      //   this.isDragging = true;
      // });

      // polygon.on("dragmove", () => {
      //   // 找到当前正在拖拽的形状的索引
      //   const shapeIndex = this.shapes.findIndex(s => s.shape === polygon);
      //   if (shapeIndex === -1) return;

      //   // 更新点的位置
      //   const polygonPoints = polygon.points();
      //   for (let i = 0; i < 4; i++) {
      //     this.shapes[shapeIndex].points[i] = {
      //       x: polygonPoints[i * 2],
      //       y: polygonPoints[i * 2 + 1],
      //     };
      //   }

      //   // 更新点标记的位置
      //   this.updatePointMarkers();
      // });

      // polygon.on("dragend", () => {
      //   this.isDragging = false;
      //   this.setData();
      // });

      return polygon;
    },
    updatePointMarkers() {
      // 移除所有点标记
      this.layer.children.forEach((node) => {
        if (node instanceof Konva.Circle) {
          node.destroy();
        }
      });

      // 重新绘制所有形状的点标记
      this.shapes.forEach((shape) => {
        shape.points.forEach((point) => {
          const circle = this.drawPoint(point.x, point.y);
          this.layer.add(circle);
        });
      });

      this.layer.batchDraw();
    },
    clearDraw() {
      this.currentPoints = [];
      this.shapes = [];
      this.line = null;
      this.isDrawing = false;
      this.layer.destroyChildren();
    },
    setData() {
      // 发送所有平行四边形的数据
      this.$emit("setData", {
        w: this.width,
        h: this.height,
        shapes: this.shapes.map((shape) => ({
          points: shape.points.map((p) => [p.x, p.y]),
        })),
      });
    },
  },
};
</script>
  <style scoped>
.polygon-draw {
  width: 100%;
  height: 100%;
}
</style>
      