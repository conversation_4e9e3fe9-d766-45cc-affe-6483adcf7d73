<template>
  <div class="polygon-draw" ref="container"></div>
</template>
    
    <script>
import Konva from "konva";

export default {
  props: {
    areaPoints: {
      type: Object,
      default() {
        return {};
      },
    },
  },
  watch: {
    areaPoints: {
      handler() {
        // this.init();
      },
      immediate: true,
    },
  },
  data() {
    return {
      stage: null, // 存储Konva舞台
      layer: null, // 存储Konva图层
      points: [], // 存储所有点击的点
      line: null, // 存储绘制的连线
      shape: null, // 存储绘制的多边形
      currentLine: [], // 存储实时鼠标位置到各点之间的连线
      isDrawing: false,
      width: 0,
      height: 0,
    };
  },
  mounted() {
    // 初始化Konva舞台
    this.initKonva();
    window.addEventListener('resize', this.handleResize);
  },
  beforeDestroy() {
    // 移除监听器
    window.removeEventListener('resize', this.handleResize);
  },
  methods: {
    handleResize() {
      const container = this.$refs.container;
      const { width, height } = container.getBoundingClientRect();
      
      // 更新舞台大小
      this.width = width;
      this.height = height;
      this.stage.width(width);
      this.stage.height(height);
      
      this.layer.destroyChildren();
      // 重新绘制所有图形
      this.initArea();
    },
    initKonva() {
      const container = this.$refs.container;
      const { width, height } = container.getBoundingClientRect();
      this.width = width;
      this.height = height;
      // 创建Konva舞台
      this.stage = new Konva.Stage({
        container: container,
        width: width,
        height: height,
      });

      // 创建图层
      this.layer = new Konva.Layer();
      this.stage.add(this.layer);

      // 设置监听事件
      this.stage.on("mousedown", this.handleMouseDown);
      this.stage.on("mousemove", this.handleMouseMove);
      this.stage.on("contextmenu", this.handleRightClick);
      if (this.areaPoints) {
        this.initArea();
      }
    },
    initArea() {
      if (
        this.areaPoints &&
        this.areaPoints.points &&
        Array.isArray(this.areaPoints.points)
      ) {
        this.points = this.areaPoints.points.map((d) => {
          return {
            x: this.getPointerPosition(d[0], "x"),
            y: this.getPointerPosition(d[1], "y"),
          };
        });
        // 创建闭合的多边形
        const polygon = this.drawLine();

        this.layer.add(polygon);
        this.layer.batchDraw();
      }
    },
    getPointerPosition(value, point) {
      const { w, h } = this.areaPoints;
      if (point === "x") {
        return (value / w) * this.width;
      } else {
        return (value / h) * this.height;
      }
    },
    handleMouseDown(event) {
      if (this.points.length > 0 && !this.isDrawing) return;
      // 获取鼠标点击位置
      const mousePos = this.stage.getPointerPosition();
      this.points.push(mousePos);
      this.isDrawing = true;

      // 在画布上绘制小方块标记点击点
      const point = this.drawPoint(mousePos.x, mousePos.y);

      // 创建连线
      if (this.points.length > 1) {
        const line = new Konva.Line({
          points: [
            this.points[this.points.length - 2].x,
            this.points[this.points.length - 2].y,
            mousePos.x,
            mousePos.y,
          ],
          stroke: "#FF0303",
          strokeWidth: 1,
        });
        this.layer.add(line);
      }

      // 添加点标记
      this.layer.add(point);

      // 刷新画布
      this.layer.batchDraw();
    },

    handleMouseMove(event) {
      if (!this.isDrawing) return;
      const mousePos = this.stage.getPointerPosition();

      // 清除所有的实时连线
      if (this.line) {
        this.line.destroy();
      }

      // 绘制所有的点和已完成的连线
      this.layer.children.forEach((node) => node.destroy());

      // 重新绘制所有的点和线
      this.points.forEach((point) => {
        const circle = this.drawPoint(point.x, point.y);
        this.layer.add(circle);
      });

      // 绘制连线
      const line = this.drawLine(mousePos);
      this.layer.add(line);

      // 更新当前连线
      this.line = line;

      // 重绘画布
      this.layer.batchDraw();
    },

    handleRightClick(event) {
      event.evt.preventDefault();
      if (!this.isDrawing) return;
      // 如果点位数量大于2个，则闭合多边形并填充
      if (this.points.length > 2) {
        const mousePos = this.stage.getPointerPosition();
        this.points.push(mousePos);

        // 创建闭合的多边形
        const polygon = this.drawLine();

        this.layer.add(polygon);
        this.layer.batchDraw();

        // 重置点和实时线条
        //   this.points = [];
        //   this.line = null;
        this.isDrawing = false;
        this.setData();
      }
    },

    getLinePoints(mousePos = null) {
      const points = this.points.flatMap((point) => [point.x, point.y]);

      // 如果传入了鼠标位置，动态添加到当前连线的点
      if (mousePos) {
        points.push(mousePos.x, mousePos.y);
      }

      return points;
    },
    drawPoint(x, y) {
      const point = new Konva.Circle({
        x: x,
        y: y,
        radius: 4,
        fill: "#0000FF",
        // stroke: "black",
        // strokeWidth: 2,
      });
      return point;
    },
    drawLine(mousePos) {
      const polygon = new Konva.Line({
        points: this.getLinePoints(mousePos),
        fill: "rgba(255,255,255, 0.2)",
        stroke: "#FF0303",
        strokeWidth: 1,
        closed: true,
        lineJoin: "round",
      });
      return polygon;
    },
    clearDraw() {
      this.points = [];
      this.line = null;
      this.isDrawing = false;
      this.layer.destroyChildren();
    },
    setData() {
      this.$emit("setData", {
        w: this.width,
        h: this.height,
        points: this.points.map((d) => {
          return [d.x, d.y];
        }),
      });
    },
  },
};
</script>
<style scoped>
.polygon-draw {
  width: 100%;
  height: 100%;
}
</style>
    