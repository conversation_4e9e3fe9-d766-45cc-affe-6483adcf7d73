<template>
  <div class="time-range-slider" ref="sliderRef">
    <!-- 使用 el-slider 组件 -->
    <el-slider
      v-model="timeRange"
      range
      :min="0"
      :max="24"
      :step="0.5"
      @change="handleChange"
      @input="inputHandle"
      :show-tooltip="false"
      :format-tooltip="formatTime"
      tooltip-class="tooltipClass"
    ></el-slider>
    <div class="tooltipClass" :style="{ left: startLeft + 'px' }">
      {{ formatTime(timeRange[0]) }}
    </div>
    <div class="tooltipClass" :style="{ left: endLeft + 'px' }">
      {{ formatTime(timeRange[1]) }}
    </div>
  </div>
</template>
  
  <script>
export default {
  name: "TimeRangeSlider",
  props: {
    range: {
      type: Object,
      default: () => {
        return {
          start: 0,
          end: 24,
        };
      },
    },
  },
  watch: {
    range: {
      handler(newVal) {
        this.timeRange = [
          this.convertTimeToNumber(newVal.start),
          this.convertTimeToNumber(newVal.end),
        ];
        this.$nextTick(() => {
          this.getStyleHandle();
        })
      },
      deep: true,
      immediate: true,
    },
  },
  data() {
    return {
      timeRange: [0, 24],
      startLeft: 0,
      endLeft: 0,
    };
  },
  mounted() {
    this.getStyleHandle();
    let that = this
    const observer = new IntersectionObserver((entries) => {
      if(entries[0].isIntersecting) {
        that.getStyleHandle();
      }
    });
    observer.observe(this.$el);
  },
  methods: {
    // 格式化时间，将小数转换为 HH:mm 格式
    formatTime(time) {
      const hours = Math.floor(time); // 获取小时部分
      const minutes = Math.round((time - hours) * 60); // 获取分钟部分
      if (time === 24) {
        return "23:59";
      } else {
        return `${String(hours).padStart(2, "0")}:${String(minutes).padStart(
          2,
          "0"
        )}`;
      }
    },
    // 将 HH:mm 格式的时间字符串转换为小数形式的数字
    convertTimeToNumber(timeStr) {
      if (timeStr === "23:59") {
        return 24; 
      }
      // 以冒号为分隔符拆分时间字符串
      const [hoursStr, minutesStr] = timeStr.split(":");
      const hours = parseInt(hoursStr, 10);
      const minutes = parseInt(minutesStr, 10);
      const minutesAsHours = minutes / 60;
      return hours + minutesAsHours;
    },
    // 当滑块值改变时触发
    handleChange(value) {
      this.$emit("change", {
        start: this.formatTime(value[0]),
        end: this.formatTime(value[1]),
      });
    },
    inputHandle() {
      this.getStyleHandle();
    },
    getStyleHandle() {
      let rectRef = this.$refs.sliderRef;
      if(rectRef) {
        let rect = rectRef.getBoundingClientRect();
        let step = rect.width / 24;
        this.startLeft = step * this.timeRange[0] - 20;
        this.endLeft = step * this.timeRange[1] - 20;
      }
      // let rectDow = document.querySelectorAll(".time-range-slider");
      // if (rectDow[0]) {
      //   let rect = rectDow[0].getBoundingClientRect();
      //   let step = rect.width / 24;
      //   this.startLeft = step * this.timeRange[0] - 20;
      //   this.endLeft = step * this.timeRange[1] - 20;
      // }
    },
  },
};
</script>
  
  <style scoped>
.time-range-slider {
  width: 100%;
  position: relative;
}

.time-labels {
  display: flex;
  justify-content: space-between;
  margin-top: 10px;
  font-size: 14px;
  color: #666;
}
.tooltipClass {
  display: inline-block;
  border-radius: 4px;
  background: #0d70f2;
  padding: 3px 5px;
  color: #fff;
  position: absolute;
  top: -15px;
  font-size: 12px;
}
</style>
  <style>
.tooltipClass.is-dark {
  background: #0d70f2;
  padding: 3px 5px;
}
.el-tooltip__popper .popper__arrow {
  display: none;
}
</style>