<template>
  <div class="title-cell-box">
    <div class="title-cell-header" :class="{'title-cell-header-type': type}">
      <div class="header-title">
        <span class="title-border"></span>
        <span class="title-text">{{ title }}</span>
      </div>
      <slot name="headerDesc">
        <div class="header-desc" v-if="desc">
          {{ desc }}
        </div>
      </slot>
      
    </div>
    <div class="title-cell-content">
      <slot></slot>
    </div>
  </div>
</template>

<script>
export default {
  name: "titleCell",
  props: {
    title: {
      type: String,
      default: "",
    },
    desc: {
      type: String,
      default: "",
    },
    type: {
      type: String,
      default: ''
    }
  },
  data() {
    return {};
  },
}
</script>

<style lang="scss" scoped>
.title-cell-box {
  width: 100%;
  background: #fff;
  border-radius: 4px;
}
.title-cell-header {
  height: 48px;
  line-height: 48px;
  padding: 0 16px;
  box-sizing: border-box;
  border-bottom: 1px solid #ededed;
  display: flex;
  align-items: center;
  justify-content: space-between;
}
.title-cell-header-type {
  padding: 0
}
.header-title {
  display: flex;
  align-items: center;
}
.title-cell-header .title-text {
  font-size: 16px;
  font-weight: 700;
  color: #333;
}
.title-border {
  display: inline-block;
  width: 6px;
  height: 16px;
  background: #0d70f2;
  margin-right: 8px;
}
.header-desc {
  font-size: 14px;
  color: #999;
}
</style>