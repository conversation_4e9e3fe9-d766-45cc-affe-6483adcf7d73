import Vue from 'vue'
import App from './App.vue'
import router from './router/index';
import store from './store/index';
import '@/assets/style/index.css'

import ELEMENT from 'element-dse-ui'
import "element-dse-ui/lib/theme-chalk/index.css"

import dictionaryUtil from '@/utils/dictionaryUtil';

import dseVideoPlayer from "dse-video-player";
import 'dse-video-player/packages/style.css'
Vue.use(dseVideoPlayer)

Vue.use(ELEMENT)

import VueEasyLightbox from 'vue-easy-lightbox'
Vue.use(VueEasyLightbox)



Vue.config.productionTip = false

Vue.prototype.dictCodes = dictionaryUtil;

new Vue({
  router,
  store,
  render: h => h(App),
}).$mount('#app')
