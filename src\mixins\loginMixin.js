import JSEncrypt from 'jsencrypt'
import DseLogin from 'DseLogin';
const PUBLICKEY = "-----BEGIN PUBLIC KEY-----MFwwDQYJKoZIhvcNAQEBBQADSwAwSAJBAN1rZQMG+eY1DjFE4SixU5BTs5DtAETxB/rFj9kqUlpuc+AAKEd9U6YsEwYOXzqyRk5/jmE4tOH04oaMC1rv0jkCAwEAAQ==-----END PUBLIC KEY-----"

const loginMixin = {
  data() {
    return {
      sysTitle: window.DSE.videoSysTitle,
      userInfo: {
        user: "",
        password: "",
      },
      inputType: "password",
      rules: {
        user: [{ required: true, message: "请输入用户名", trigger: "blur" }],
        password: [{ required: true, message: "请输入密码", trigger: "blur" }],
      },
    }
  },
  mounted() {

  },
  methods: {
    btnLoginClick() {
      this.$refs["ruleForm"].validate((valid) => {
        if (valid) {
          //正常代码 获取token
          let user = {
            user: this.userInfo.user,
            pwd: this.userInfo.password
          }
          DseLogin.getToken(window.DSE, user);
        }
      })

    },
    btnEncryptLoginClick() {
      this.$refs["ruleForm"].validate((valid) => {
        if (valid) {
          var encrypt = new JSEncrypt();
          encrypt.setPublicKey(PUBLICKEY)
          let user = {
            user: encodeURIComponent(encrypt.encrypt(this.userInfo.user)),
            pwd: encodeURIComponent(encrypt.encrypt(this.userInfo.password)),
            authType: window.userInfo.authType
          }
          //正常代码 获取token
          DseLogin.getToken(window.DSE, user).then(() => {

          }).catch(err => {
            console.log(err)
          });
        }
      })


    },
  },
};

export {
  loginMixin
};