
const menuTreeMixin = {
  props: {
    title: {
      type: String,
      default: "",
    },
    treeData: {
      type: Array,
      default: function () {
        return [];
      },
    },
    lazy: {
      type: Boolean,
      default: true,
    },
    url: {
      type: String,
      default: "",
    },
    autoParam: {
      type: Array,
      default: function () {
        return [];
      },
    },
    otherParam: {
      type: Object,
      default: function () {
        return {};
      },
    },
    listKey: {
      type: String,
      default: "",
    },
    nodeKey: {
      type: String,
      default: "id",
    },
    treeProps: {
      type: Object,
      default: function () {
        return {};
      },
    },
    expandedKeys: {
      type: Array,
      default: function () {
        return [];
      },
    },
    currentNodeKey: {
      type: String,
      default: "",
    },
    isShowIcon: {
      type: Boolean,
      default: false,
    },
    btnList: {
      type: Array,
      default: function () {
        return [];
      },
    },
    firstNode: {
      type: Object,
      default: function () {
        return {};
      },
    },
    draggable: {
      type: Boolean,
      default: false,
    },
    //显示下拉菜单
    contextMenu: {
      type: Object,
      default: function () {
        return {
          visible: false,
        };
      },
    },
    showBtn: {
      type: Boolean,
      default: false,
    }
  },
  watch: {
    expandedKeys: {
      handler(value) {
        this.defaultExpandedKeys = value;
      },
      deep: true,
      immediate: true,
    },
    currentNodeKey: {
      handler(value) {
        setTimeout(() => {
          if(value) {
            this.$refs.treeRef && this.$refs.treeRef.setCurrentKey(value);
          } else {
            this.$refs.treeRef && this.$refs.treeRef.setCurrentKey(null);
          }
        }, 50)
      },
      immediate: true
    }
  },
  data() {
    return {
      keyWord: "",
      data: [],
      defaultProps: {},
      count: 0,
      defaultExpandedKeys: [],
      treeShow: true,
      allTreeData: [],
      dragging: false
    };
  },
  mounted() {
    this.defaultExpandedKeys = this.expandedKeys;
  },
  methods: {
    initTree() { },
    handleNodeClick(data) {
      this.$emit("menu-item-click", data);
    },
    searchHandle() {
      this.$refs.treeRef.filter(this.keyWord);
    },
    filterNode(value, data) {
      if (!value) return true;
      return data[this.treeProps.label].indexOf(value) !== -1;
    },
    reloadTree() {
      this.count = 0
      this.treeShow = false;
      setTimeout(() => {
        this.treeShow = true;
      }, 100);
    },
    getAllTreeData() {
      return this.allTreeData;
    },
    handleCommand(command, node) {
      switch (command) {
        case "delete":
          this.$emit("delete-click", node);
          break;
        case "add":
          this.$emit("add-click", node);
          break;
        case "edit":
          this.$emit("edit-click", node);
          break;
        case "auth":
            this.$emit("auth-click", node);
            break;
        case "authView":
          this.$emit("auth-view-click", node);
          break;
        default:
          break;
      }
    },
    allowDrag(draggingNode) {
      if (this.listKey === "departmentTree") {
        let currentNode = draggingNode.data;
        if (currentNode.type === "ORG") {
          return false
        }
      }
      return true
    },
    allowDrop(draggingNode, dropNode, type) {
      if (draggingNode.level === dropNode.level && type !== "inner") {
        let currentNode = draggingNode.data;
        let targetNode = dropNode.data;
        if (currentNode.pid === targetNode.pid) {
          if (this.listKey === "departmentTree" && targetNode.type === "ORG") {
            return false
          } else {
            return true
          }
        }
      } else {
        return false
      }
    },
    handleNodeDragStart(draggingNode, event) {
      const element = event.target;
    },
    handleDrop(draggingNode, dropNode, dropType, event) {
      let currentNode = draggingNode.data;
      let targetNode = dropNode.data;
      let lists = []
      let sortParams = []
      if (this.lazy) {
        this.allTreeData.forEach(item => {
          const node = item.find(d => d.id === currentNode.id);
          if (node) {
            lists = item
          }
        })
        sortParams = this.getSortedList(lists, currentNode, targetNode, dropType)
      } else {
        lists = this.iterationHandle(this.treeData, currentNode)
        sortParams = lists.map((item, index) => {
          return {
            id: item.id,
            orderIndex: index + 1
          }
        })
      }
      this.$emit('drop', sortParams)
    },
    iterationHandle(treeData, node) {
      const targetItem = treeData.find(d => d.id === node.id);
      if(targetItem) {
        return treeData
      } else {
        for(let item of treeData) {
          if (item.childsNode && item.childsNode.length > 0) {
            const foundInChild = this.iterationHandle(item.childsNode, node)
            if(foundInChild) {
              return foundInChild
            }
          }
        }
      }
    },
    getSortedList(lists, currentNode, targetNode, dropType) {
      const currentIndex = lists.findIndex(d => currentNode.id === d.id)
      lists.splice(currentIndex, 1)
      const targetIndex = lists.findIndex(d => targetNode.id === d.id)
      if (dropType === "before") {
        lists.splice(targetIndex, 0, currentNode)
      } else {
        lists.splice(targetIndex + 1, 0, currentNode)
      }
      let sortParams = lists.map((item, index) => {
        return {
          id: item.id,
          orderIndex: index + 1
        }
      })
      return sortParams
    }
  },
};

export {
  menuTreeMixin
};