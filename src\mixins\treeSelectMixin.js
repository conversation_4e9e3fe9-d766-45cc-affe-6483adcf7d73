
const treeSelectMixin = {
  props: {
    lazy: {
      type: Boolean,
      default: true,
    },
    url: {
      type: String,
      default: "",
    },
    defaultValue: {
      type: [String, Array],
      default: "",
    },
    autoParam: {
      type: Array,
      default: function () {
        return [];
      },
    },
    otherParam: {
      type: Object,
      default: function () {
        return {};
      },
    },
    treeProps: {
      type: Object,
      default: function () {
        return {};
      },
    },
    nodeKey: {
      type: String,
      default: "id",
    },
    disabled: {
      type: Boolean,
      default: false,
    },
    disabledFilter: {
      type: Boolean,
      default: false,
    },
    isShowIcon: {
      type: Boolean,
      default: false,
    },
    listKey: "",
    treeValue: {
      type: [String, Array],
      default: "",
    },
    expandedKeys: {
      type: Array,
      default: function () {
        return [];
      },
    },
    treeData: {
      type: Array,
      default: function () {
        return [];
      },
    },
    multiple: {
      type: Boolean,
      default: false,
    },
    disabledValue: {
      type: String,
      default: ""
    },
    filterable: {
      type: <PERSON>olean,
      default: false,
    },
    filterId: {
      type: String,
      default: '',
    }
  },
  watch: {
    treeValue: {
      handler(val) {
        this.selectValue = val == "-1" ? "" : val;
      },
      immediate: true,
    },
    expandedKeys: {
      handler(value) {
        this.defaultExpandedKeys = value;
      },
      immediate: true,
      deep: true,
    },
  },
  data() {
    return {
      defaultExpandedKeys: [],
      count: 0,
      selectValue: "",
      allTreeData: [],
    };
  },
  mounted() {},
  methods: {
    changeHandle(node) {
      this.$emit("change", node);
    },
    getAllTreeData() {
      return this.allTreeData;
    },
    removeTag(tag) {
      this.$emit('remove-tag', tag)
    },
  },
}


export {
  treeSelectMixin
};