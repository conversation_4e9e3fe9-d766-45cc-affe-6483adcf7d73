import Vue from "vue";
import Router from "vue-router";
import DseLogin from 'DseLogin';
Vue.use(Router);

import { getSysconfData } from "@/utils/sysConfig";

let routes = [
  {
    path: '/login',
    name: 'login',
    title: '登录',
    component: () => import( /* webpackChunkName: "login" */ '@/views/login/index'),
  },
  {
    path: '/login1',
    name: 'login1',
    title: '登录',
    component: () => import( /* webpackChunkName: "login1" */ '@/views/login1/index'),
  },
  {
    path: '/monPointsCfg',
    name: 'monPointsCfg',
    title: '监测点配置',
    component: () => import( /* webpackChunkName: "monPointsCfg" */ '@/views/monPointsCfg/index'),
  },
  {
    path: '/monPointsCfg/aiCfg',
    name: 'monPointsCfgAiCfg',
    title: '监测点配置-AI智能算法配置',
    component: () => import( /* webpackChunkName: "monPointsAiCfg" */ '@/views/monPointsCfg/config'),
  },
  {
    path: '/serverCfg',
    name: 'serverCfg',
    title: '视频服务器配置',
    component: () => import( /* webpackChunkName: "serverCfg" */ '@/views/serverCfg/index'),
  },
  {
    path: '/statistics',
    name: 'statistics',
    title: '在线统计',
    component: () => import( /* webpackChunkName: "statistics" */ '@/views/statistics/index'),
  },
  {
    path: '/tagCfg',
    name: 'tagCfg',
    title: '视频标签配置',
    component: () => import( /* webpackChunkName: "tagCfg" */ '@/views/tagCfg/index'),
  },
  {
    path: '/videoView',
    name: 'videoView',
    title: '查看视频',
    component: () => import( /* webpackChunkName: "videoView" */ '@/views/videoView/index'),
  },
  {
    path: '/viewPreview',
    name: 'viewPreview',
    title: '视频窗口',
    component: () => import( /* webpackChunkName: "viewPreview" */ '@/views/viewPreview/index'),
  },
  {
    path: '/extend',
    name: 'extend',
    title: '监控点扩展管理',
    component: () => import( /* webpackChunkName: "extend" */ '@/views/extend/index'),
  },
  {
    path: '/alarmCenter',
    name: 'alarmCenter',
    title: '告警中心',
    component: () => import( /* webpackChunkName: "alarmCenter" */ '@/views/alarmCenter/index'),
  },
  {
    path: '/monitorVideo',
    name: 'monitorVideo',
    title: '视频监控',
    component: () => import( /* webpackChunkName: "monitorVideo" */ '@/views/monitorVideo/index'),
  },
  {
    path: '/monitorView',
    name: 'monitorView',
    title: '视频监控',
    component: () => import( /* webpackChunkName: "monitorView" */ '@/views/monitorVideo/detail'),
  },
  {
    path: '/monitorResult',
    name: 'monitorResult',
    title: '识别成果',
    component: () => import( /* webpackChunkName: "monitorResult" */ '@/views/monitorResult/index'),
  },
  {
    path: '/monitorResult/detail',
    name: 'monitorResultDetail',
    title: '识别成果-详情',
    component: () => import( /* webpackChunkName: "monitorResultDetail" */ '@/views/monitorResult/detail'),
  },
  {
    path: '/webPlugin',
    name: 'webPlugin',
    title: '海康插件播放',
    component: () => import( /* webpackChunkName: "webPlugin" */ '@/views/webPlugin/index'),
  },
  {
    path: '/haiKangPlugin',
    name: 'haiKangPlugin',
    title: '海康插件播放',
    component: () => import( /* webpackChunkName: "haiKangPlugin" */ '@/views/haiKangPlugin/index'),
  },
]

let router = new Router({
  mode: process.env.NODE_ENV === 'production' ? 'history' : 'history',
  base: process.env.NODE_ENV === 'production' ? '/dse-video-web/' : '/dse-video-web/',
  routes: routes,
});
// await getSysconfData();
if (window.DSE.useTokenServer) {
  router.beforeEach(async (to, from, next) => {
    if (DseLogin.isLogin()) {
      next()
    } else {
      if (to.path === '/login' || to.path === '/login1') {
        next()
      } else {
        let tempUrl = window.top.location.href
        let tempUrl1 = tempUrl.replace('undefined', '')
        DseLogin.setItemToLocal('tempUrl', tempUrl1)
        DseLogin.toAddressBarUrl(window.DSE)
      }
    }
  });
}

export default router;