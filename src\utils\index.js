
import axios from "axios";
export function getQueryParams(url) {
  const parts = url.split("?");
  const path = parts[0];
  const query = parts[1];

  const params = {};
  if (query) {
    query.split("&").forEach((part) => {
      const [key, value] = part.split("=");
      params[decodeURIComponent(key)] = decodeURIComponent(value);
    });
  }

  return { url: path, params };
}

export function convertDateTime(input) {
  // 解析输入字符串
  const year = input.substring(0, 4);
  const month = input.substring(4, 6);
  const day = input.substring(6, 8);
  const hours = input.substring(9, 11);
  const minutes = input.substring(11, 13);
  const seconds = input.substring(13, 15);

  // 重新格式化日期时间
  const formattedMonth = ("0" + month).slice(-2);
  const formattedDay = ("0" + day).slice(-2);
  const formattedHours = ("0" + hours).slice(-2);
  const formattedMinutes = ("0" + minutes).slice(-2);
  const formattedSeconds = ("0" + seconds).slice(-2);

  return `${year}-${formattedMonth}-${formattedDay}T${formattedHours}:${formattedMinutes}:${formattedSeconds}`;
}

export function formatTree(tree, list) {
  if (Array.isArray(tree)) {
    for (let item of tree) {
      let node = list.find(d => d.id === item.id);
      if(node) {
        item.inline = node.inline
      }
      if (item.children && item.children.length > 0) {
        formatTree(item.children, list);
      }
    }
  }
}


export function formatTreeData(tree, item, disabled) {
  tree.forEach(d => {
    d.serverCode = item.code
    d.pId = d.dataPid
    if(disabled) {
      d.disabled = !d.selective;
    }
    d.id = d.id || d.dataId + item.code
    if(d.children && d.children.length) {
      formatTreeData(d.children, item, disabled)
    }
  })
}


export function getDataAuthData(list, disabled) {
  return new Promise((resolve, reject) => {
    if (list.length === 0) {
      resolve([]);
      return [];
    }
    let promiseList = [];
    let loginToken = window.localStorage.getItem("__app_token__");
    list.forEach((d) => {
      let promiseItem = new Promise((reso, rej) => {
        axios({
          method: "get",
          url: d.url,
          headers: {
            Authorization: loginToken,
          },
        })
          .then((res) => {
            if (res.data.code === 200) {
              reso(res.data.data);
            } else {
              rej(res);
            }
          })
          .catch((err) => {
            rej(err);
          });
      });
      promiseList.push(promiseItem);
    });
    Promise.allSettled(promiseList)
      .then((res) => {
        list.forEach((item, index) => {
          let nodeTree = res[index].value
          formatTreeData(nodeTree, item, disabled)
          item.children = nodeTree.map(d => {
            return {
              ...d,
              pId: item.id
            }
          });
          item.dataName = item.name;
          item.dataId = item.id;
          if(disabled) {
            item.disabled = true
          }
          
        });
        resolve(list)
      })
      .catch(() => {
        let treeData = list.map((d) => {
          return {
            ...d,
            dataName: d.name,
            dataId: d.id
          };
        });
        resolve(treeData)
      });
  })
}


/** 
 * 判断是否是JSON字符串
 * @param {string} str 要判断的字符串
 * @returns {boolean} 是否是JSON字符串
 */
export const isJSONString = (str) => {
  try {
    JSON.parse(str);
    return true;
  } catch (e) {
    return false;
  }
}

export const findItemByCode = (tree, code) => {
  for (const item of tree) {
    if (item.cameraIndexCode === code) {
      return item;
    }
    if (item.children) {
      const found = findItemByCode(item.children, code);
      if (found) return found;
    }
  }
  return null;
}

export const serverOptions = {
  1: "haiKang",
  2: "yingShi",
  3: "daHua",
};