import {
  directOperateDhRequest,
  cameraOperateDhRequest,
  controlCameraHikRequest,
  getEzvizAccessToken,
  inokeDaHuaRequest,
  inokeHaiKangRequest,
} from "@/api/play/playData";

import {
  startYingShiPtzRequest,
  stopYingShiPtzRequest,
} from "@/api/yingShi/yingShiData";

// 通用基础类
class CameraController {
  constructor() {
    this.toolConfig = {};
  }

  getControlSpeed(speed) {
    return speed === 0 ? 1 : speed === 100 ? 8 : 4;
  }
}

// 大华控制器
class DahuaController extends CameraController {
  constructor() {
    super();
    this.toolConfig = {
      LEFT_DOWN: 6,
      RIGHT_DOWN: 8,
      LEFT_UP: 5,
      RIGHT_UP: 7,
      UP: 1,
      DOWN: 2,
      LEFT: 3,
      RIGHT: 4,
    };
  }

  controlCamera(command, action, speed, cameraIndexCode) {
    if (this.toolConfig[command]) {
      this.directOperateHandle(command, action, speed, cameraIndexCode);
    } else {
      this.cameraOperateHandle(command, action, speed, cameraIndexCode);
    }
  }

  directOperateHandle(command, action, speed, cameraIndexCode) {
    const params = {
      channelId: cameraIndexCode,
      direct: this.toolConfig[command],
      command: action === "start" ? 1 : 0,
      stepX: this.getControlSpeed(speed),
      stepY: this.getControlSpeed(speed),
    };
    directOperateDhRequest(params);
  }

  cameraOperateHandle(command, action, speed, cameraIndexCode) {
    const params = {
      channelId: cameraIndexCode,
      operateType: 2,
      command: action === "start" ? 1 : 0,
      step: this.getControlSpeed(speed),
      direct: command === "ZOOM_IN" ? 1 : command === "ZOOM_OUT" ? 2 : 1,
    };
    cameraOperateDhRequest(params);
  }
  // 定位预置位
  gotoPreset(preset, message) {
    let params = {
      serverId: preset.serverId,
      url: "/evo-apigw/admin/API/DMS/Ptz/OperatePresetPoint",
      method: "POST",
      params: {
        data: {
          channelId: preset.cameraIndexCode,
          presetPointCode: preset.value,
          presetPointName: preset.name,
          operateType: 1,
        },
      },
    };
    inokeDaHuaRequest(params).then((res) => {
      if (res.code === 200) {
        message({
          type: "success",
          message: "执行成功",
        });
      } else {
        message({
          type: "warning",
          message: res.message,
        });
      }
    });
  }
}

// 海康控制器
class HaikangController extends CameraController {
  controlCamera(command, action, speed, cameraIndexCode) {
    const params = {
      cameraIndexCode,
      action: action === "start" ? 0 : 1,
      command,
      speed: speed === 0 ? 1 : speed === 100 ? 100 : 50,
    };
    controlCameraHikRequest(params);
  }
  // 定位预置位
  gotoPreset(preset, message) {
    let params = {
      serverId: preset.serverId,
      url: "/api/video/v1/ptzs/controlling",
      method: "POST",
      params: {
        cameraIndexCode: preset.cameraIndexCode,
        action: 0,
        command: "GOTO_PRESET",
        presetIndex: Number(preset.value),
        speed: 50,
      },
    };
    inokeHaiKangRequest(params).then((res) => {
      if (res.code === 200) {
        message({
          type: "success",
          message: "执行成功",
        });
      } else {
        message({
          type: "warning",
          message: res.message,
        });
      }
    });
  }
}

// 萤石控制器
class YingshiController extends CameraController {
  constructor() {
    super();
    this.toolConfig = {
      LEFT_DOWN: 5,
      RIGHT_DOWN: 7,
      LEFT_UP: 4,
      RIGHT_UP: 6,
      UP: 0,
      DOWN: 1,
      LEFT: 2,
      RIGHT: 3,
      FOCUS_FAR: 11,
      IRIS_REDUCE: 10,
      ZOOM_IN: 9,
      ZOOM_OUT: 8,
    };
    this.accessToken = "";
    this.deviceSerial = "";
    this.channelNo = "";
  }

  controlCamera(command, action, speed, cameraIndexCode) {
    this.cameraIndexCode = cameraIndexCode;
    getEzvizAccessToken(cameraIndexCode).then((res) => {
      if (res.code === 200) {
        this.accessToken = res.data || "";
        const [deviceSerial, channel] = cameraIndexCode.split("-");
        this.deviceSerial = deviceSerial || "";
        this.channelNo = channel ? Number(channel) : 0;
        this.controlHandle(command, action, speed);
      }
    });
  }

  controlHandle(command, action, speed) {
    const params = {
      accessToken: this.accessToken,
      deviceSerial: this.deviceSerial,
      channelNo: this.channelNo,
      direction: this.toolConfig[command],
      speed: speed === 0 ? 0 : speed === 100 ? 2 : 1,
    };
    const request =
      action === "start" ? startYingShiPtzRequest : stopYingShiPtzRequest;
    request(params);
  }
  gotoPreset(preset, message) {}
}

// 工厂类
class CameraControllerFactory {
  static createController(brand) {
    switch (brand.toLowerCase()) {
      case "dahua":
        return new DahuaController();
      case "haikang":
        return new HaikangController();
      case "yingshi":
        return new YingshiController();
      default:
        throw new Error(`Unknown brand: ${brand}`);
    }
  }
}

export default CameraControllerFactory;

// // 使用示例
// try {
//   const dahuaController = CameraControllerFactory.createController("dahua");
//   dahuaController.controlCamera("UP", "start", 50, "camera1");

//   const haikangController = CameraControllerFactory.createController("haikang");
//   haikangController.controlCamera("ZOOM_IN", "stop", 100, "camera2");

//   const yingshiController = CameraControllerFactory.createController("yingshi");
//   yingshiController.controlCamera("LEFT", "start", 0, "serial-1");
// } catch (error) {
//   console.error(error.message);
// }
