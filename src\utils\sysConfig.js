import { getSysSettingList } from "@/api/sysConfig/sysConfigData";
import { isJSONString } from './index'

const getServerUrl = () => {
  return (
    window.location.protocol +
    "//" +
    window.location.hostname +
    ":" +
    window.location.port
  );
};

export const getSysconfData = () => {
  return new Promise((resolve, reject) => {
    let urlList = ["userLoginFailureJumpUrl", "userHome", "NotFound"];
    let sysConfig = null;
    try {
      if (window.opener && window.opener.localStorage) {
        sysConfig = window.opener.localStorage.getItem("sysConfig");
      } else {
        sysConfig = window.localStorage.getItem("sysConfig");
      }
    } catch (error) {
      console.log("无法访问 localStorage:", error);
    }
    if (sysConfig && isJSONString(sysConfig)) {
      sysConfig = JSON.parse(sysConfig);
      window.DSE = { ...window.DSE, ...sysConfig };
      resolve();
      return;
    }
    let params = {
      words: "",
      code: "",
      name: "",
      current: 1,
      size: 2000,
      id: "",
      startTime: "",
      endTime: "",
    };
    getSysSettingList(params).then((res) => {
      if (res.code == 200) {
        let list = res.data.records;
        let sysUrl = getServerUrl();
        sysConfig = {};
        list.forEach((item) => {
          if (item.type == "boolean") {
            sysConfig[item.code] = item.value == "true" ? true : false;
          } else if (urlList.includes(item.code)) {
            sysConfig[item.code] = sysUrl + item.value;
          } else {
            sysConfig[item.code] = item.value;
          }
        });
        localStorage.setItem("sysConfig", JSON.stringify(sysConfig));
        window.DSE = { ...window.DSE, ...sysConfig };
        resolve();
      } else {
        reject();
      }
    });
  });
};
