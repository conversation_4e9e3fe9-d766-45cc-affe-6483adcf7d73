//
import { queryDictionary4Sel } from '@/api/common/commonData';
import dictionaryUtil from '@/utils/dictionaryUtil';
//将字典项的数字转换成中文意思
export async function convertTableData(tableData = []) {
    let newData = [];
    let tableCount = tableData.length;
    let alarmTypeOptions = await queryDictionary4Sel(dictionaryUtil.alarmType, true);
    for (let i = 0; i < tableCount; i++) {
        let item = tableData[i];
        let sel = alarmTypeOptions.find(d => d.value === item.alarmType)
        // 添加隶属关系字段
        item.alarmTypeName = sel && sel.label
        newData.push(item);
    }
    return newData;
}