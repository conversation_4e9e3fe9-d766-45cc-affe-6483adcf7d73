<template>
  <el-dialog
    :title="title"
    :visible.sync="showDialog"
    :close-on-click-modal="false"
    destroy-on-close
    :before-close="closeHandle"
    width="840px"
  >
    <div class="dialog-box">
      <div class="tabs-box">
        <el-tabs v-model="activeName">
          <el-tab-pane label="报警图片" name="photo"></el-tab-pane>
          <el-tab-pane label="实时画面" name="real"></el-tab-pane>
        </el-tabs>
        <div v-if="activeName === 'photo'">
          <el-image class="list-image" :src="row.alarmPicUrl" alt="" />
        </div>
        <div v-if="activeName === 'real'&& playerForm && playerForm.cameraIndexCode" class="video-box">
          <dse-video-player
            ref="videoRef"
            v-if="playerForm && playerForm.cameraIndexCode"
            :cameraIndexCode="playerForm.cameraIndexCode"
            :keyIndex="index"
            :serverType="playerForm.jrtype"
            :name="playerForm.name"
            :transType="playerForm.transType"
            :isTool="false"
            :realtimeEnable="playerForm.realtimeEnable && isPlugins.AI"
            :isShowCollect="false"
            :isCollect="false"
            :pageTheme="pageTheme"
            :isClose="false"

          ></dse-video-player>
          <el-empty description="暂无实时画面" v-else></el-empty>
        </div>
      </div>
      <div class="form-box">
        <el-form label-position="right" label-width="100px" :model="row">
          <title-cell title="报警信息" type="1">
            <div class="form-item-box">
              <el-form-item label="报警时间：">
                <div>{{ row.createTime || "-" }}</div>
              </el-form-item>
              <el-form-item label="报警类型：">
                <div>{{ row.algorithmName || "-" }}</div>
              </el-form-item>
              <el-form-item label="报警等级：">
                <div>{{ row.alarmTypeName || "-" }}</div>
              </el-form-item>
              <el-form-item label="报警设备：">
                <div>{{ row.siteName || "-" }}</div>
              </el-form-item>
            </div>
          </title-cell>
          <title-cell title="解除信息" type="1">
            <div class="form-item-box" v-if="row.alarmStatus == 'YJC'">
              <el-form-item label="解除时间：">
                <div>{{ row.updateTime || "-" }}</div>
              </el-form-item>
              <el-form-item label="解除人：">
                <div>{{ row.updateId || "-" }}</div>
              </el-form-item>
              <el-form-item label="解除原因：">
                <div>{{ row.remark || "-" }}</div>
              </el-form-item>
            </div>
            <div class="form-item-box" v-else>
              <el-form-item label="解除原因：">
                <el-input
                  v-model="formItem.remark"
                  type="textarea"
                  :autosize="{ minRows: 2, maxRows: 6 }"
                  maxlength="200"
                  show-word-limit
                ></el-input>
              </el-form-item>
            </div>
          </title-cell>
        </el-form>
      </div>
    </div>
    <span slot="footer" class="dialog-footer" v-if="row.alarmStatus != 'YJC'">
      <el-button @click="closeHandle">取消</el-button>
      <el-button type="primary" @click="saveHandle" :loading="loading"
        >确定</el-button
      >
    </span>
  </el-dialog>
</template>
<script>
import { siteInfoRequest } from "@/api/site/siteData";
import { updateAlarmRequest } from "@/api/alarm/alarmData"
import titleCell from "@/components/titleCell/index.vue";
export default {
  name: "alarmDialog",
  components: {
    titleCell,
  },
  props: {
    row: {
      type: Object,
      default() {
        return {};
      },
    },
  },
  data() {
    return {
      title: "报警信息",
      showDialog: true,
      activeName: "photo",
      playerForm: {
        cameraIndexCode: "",
        jrtype: "",
        transType: "",
        realtimeEnable: "",
        videoServerId: "",
      },
      formItem: {
        remark: "",
      },
      pageTheme: "light",
      index: 1,
      loading: false
    };
  },
  mounted() {
    this.getDetailData();
  },
  methods: {
    getDetailData() {
      if (!this.row.siteId) return;
      siteInfoRequest(this.row.siteId).then((res) => {
        if (res.code === 200) {
          this.playerForm = res.data;
        }
      });
    },
    closeHandle() {
      this.$emit("close");
    },
    saveHandle() {
      this.loading = true
      updateAlarmRequest({
        id: this.row.id,
        remark: this.formItem.remark,
        alarmStatus: "YJC",
      }).then(res => {
        this.loading = false
        if (res.code === 200) {
          this.$message.success("解除成功");
          this.$emit("success");
        }
      }).catch(() => {
        this.loading = false
      })
    }
  },
};
</script>
<style lang="scss" scoped>
/deep/ .el-dialog__body {
  padding: 0 16px;
}
/deep/ .el-form-item {
  margin-bottom: 12px;
}
.dialog-box {
  display: flex;
}
.tabs-box {
  flex: 1;
}
.form-box {
  width: 300px;
  margin-left: 10px;
}
.form-item-box {
  padding-top: 12px;
}
.video-box {
  height: 350px;
  width: 498px;
  position: relative;
}
.list-image {
  height: 350px;
  width: 498px;
  position: relative;
}
</style>