<template>
  <div class="page-wapper">
    <div class="wapper-box">
      <div class="header-btn-box">
        <el-input
          placeholder="请输入监控点名称"
          v-model="searchItem.name"
          style="width: 300px"
        >
        </el-input>
      </div>
      <div class="header-search-box">
        <el-form
          :model="searchItem"
          label-width="112px"
          ref="formRef"
          :inline="true"
        >
          <div class="from-box">
            <el-form-item label="告警类型:">
              <el-select
                style="width: 100%"
                v-model="searchItem.algorithmCode"
                placeholder="请选择"
              >
                <el-option
                  v-for="item in algorithmOptions"
                  :key="item.code"
                  :label="item.name"
                  :value="item.code"
                >
                </el-option>
              </el-select>
            </el-form-item>
            <el-form-item label="状态:" prop="alarmStatus">
              <el-select
                style="width: 100%"
                v-model="searchItem.alarmStatus"
                placeholder="请选择"
              >
                <el-option
                  v-for="item in alarmStatusOptions"
                  :key="item.code"
                  :label="item.name"
                  :value="item.code"
                >
                </el-option>
              </el-select>
            </el-form-item>
            <el-form-item label="预警等级:" prop="alarmType">
              <pt-dict-down-list
                :selectItem="searchItem.alarmType"
                :type="dictCodes.alarmType"
                :isDefault="false"
                :addAll="true"
                @change="typeChangeHandle"
              ></pt-dict-down-list>
            </el-form-item>
            <el-form-item label="告警日期:">
              <el-date-picker
                v-model="dateRange"
                type="datetimerange"
                range-separator="-"
                start-placeholder="开始日期"
                end-placeholder="结束日期"
                value-format="yyyy-MM-dd HH:mm:ss"
                @change="rangeChange"
                style="width: 100%"
              >
              </el-date-picker>
            </el-form-item>
          </div>
        </el-form>
        <div class="search-btn-box">
          <el-button @click="resetHandle">重置</el-button>
          <el-button @click="handleSearch" type="primary">查询</el-button>
        </div>
      </div>
      <div class="statistics-box">
        <div class="statistics-item">
          告警总数<span class="statistics-num">{{
            countObject.totalCount
          }}</span
          >个，
        </div>
        <div class="statistics-item">
          轻微<span class="statistics-num status-text-Slight">{{
            countObject.slightCount
          }}</span
          >个，
        </div>
        <div class="statistics-item">
          一般<span class="statistics-num status-text-Normal">{{
            countObject.normalCount
          }}</span
          >个，
        </div>
        <div class="statistics-item">
          严重<span class="statistics-num status-text-Critical">{{
            countObject.criticalCount
          }}</span
          >个
        </div>
      </div>
      <div class="content-box" ref="contentRef" v-loading="loading">
        <div class="list-item" v-for="item in tableData" :key="item.id">
          <div class="list-image-box">
            <!--  -->
            <el-image
              class="list-image"
              :preview-src-list="previewList"
              :src="item.alarmPicUrl"
            ></el-image>
            <span class="list-status" :class="['status-' + item.alarmType]">{{
              item.alarmTypeName
            }}</span>
          </div>

          <div class="list-item-bottom" @click="openDialog(item)">
            <div
              class="bottom-left"
              :class="{ 'bottom-left-yjc': item.alarmStatus === 'YJC' }"
            >
              <div class="list-title">
                <div class="list-title-text">
                  {{ item.siteName || "" }}
                </div>
                <div
                  class="status-text"
                  :class="{ 'status-text-yjc': item.alarmStatus === 'YJC' }"
                >
                  {{ item.alarmStatus === "YJC" ? "已解除" : "未处理" }}
                </div>
              </div>
              <div class="list-dec">{{ item.algorithmName }}</div>
              <div class="list-time">{{ item.createTime }}</div>
            </div>
            <div class="btn-box" v-if="item.alarmStatus !== 'YJC'">
              <el-button type="primary" plain>解除</el-button>
            </div>
          </div>
        </div>
        <el-empty
          description="暂无数据"
          v-if="tableData.length === 0"
        ></el-empty>
      </div>
      <div class="pager-box" ref="pager">
        <el-pagination
          @size-change="handleSizeChange"
          @current-change="handleCurrentChange"
          :current-page="searchItem.pageNum"
          :page-sizes="pageSizes"
          :page-size="searchItem.pageSize"
          layout="total, ->, prev, pager,next, sizes, jumper"
          :total="total"
        >
        </el-pagination>
      </div>
    </div>
    <alarm-dialog
      v-if="showDialog"
      :row="siteRow"
      @close="closeHandle"
      @success="successHandle"
    ></alarm-dialog>
  </div>
</template>
<script>
import { algorithmListRequest } from "@/api/ai/aiData";
import {
  getAlarmListRequest,
  getAlarmCountRequest,
} from "@/api/alarm/alarmData";

import PtDictDownList from "@/components/PtDictDownList/index.vue";
import alarmDialog from "./components/alarmDialog.vue";

import { convertTableData } from "./alarmUtil";
export default {
  name: "alarmCenter",
  components: {
    PtDictDownList,
    alarmDialog,
  },
  data() {
    return {
      searchItem: {
        algorithmCode: "",
        name: "",
        alarmType: null,
        recordType: "Alarm",
        startTime: "",
        endTime: "",
        alarmStatus: "WCL",
        pageNum: 1,
        pageSize: 20,
      },
      algorithmOptions: [],
      pageSizes: [10, 20, 30, 40, 50],
      total: 0,
      dateRange: [],
      tableData: [],
      countObject: {
        totalCount: 0,
        slightCount: 0,
        normalCount: 0,
        criticalCount: 0,
      },
      loading: false,
      previewList: [],
      siteRow: {},
      showDialog: false,
      alarmStatusOptions: [
        {
          code: null,
          name: "全部",
        },
        {
          code: "WCL",
          name: "未处理",
        },
        {
          code: "YJC",
          name: "已解除",
        },
      ],
    };
  },
  mounted() {
    this.getAlgorithData();
    this.handleSearch();
  },
  methods: {
    resetHandle() {
      this.searchItem = {
        algorithmCode: "",
        name: "",
        alarmType: null,
        recordType: "Alarm",
        startTime: "",
        endTime: "",
        alarmStatus: "WCL",
        pageNum: 1,
        pageSize: 20,
      };
      this.dateRange = [];
      this.getListHandle();
    },
    getAlgorithData() {
      algorithmListRequest().then((res) => {
        if (res.code === 200) {
          this.algorithmOptions = res.data;
          this.algorithmOptions.unshift({
            code: "",
            name: "全部",
          });
        }
      });
    },
    typeChangeHandle(value) {
      this.searchItem.alarmType = value || null;
    },
    rangeChange(value) {
      if (value && value.length > 0) {
        this.searchItem.startTime = value[0];
        this.searchItem.endTime = value[1];
      } else {
        this.searchItem.startTime = "";
        this.searchItem.endTime = "";
      }
    },
    handleSearch() {
      this.searchItem.pageNum = 1;
      this.getListHandle();
    },
    handleSizeChange(val) {
      this.searchItem.pageSize = val;
      this.handleSearch();
    },
    handleCurrentChange(val) {
      this.searchItem.pageNum = val;
      this.getListHandle();
    },
    getListHandle() {
      this.loading = true;
      this.getCountData();
      getAlarmListRequest(this.searchItem)
        .then((res) => {
          this.loading = false;
          if (res.code === 200) {
            this.total = Number(res.data.total);
            let tableData = res.data.records;
            this.previewList = tableData.map((d) => d.alarmPicUrl);
            convertTableData(tableData).then((newData) => {
              this.tableData = newData;
            });
          }
        })
        .catch((err) => {
          this.loading = false;
        });
    },
    getCountData() {
      getAlarmCountRequest(this.searchItem).then((res) => {
        if (res.code === 200) {
          this.countObject = res.data;
        } else {
          this.countObject = {
            totalCount: 0,
            slightCount: 0,
            normalCount: 0,
            criticalCount: 0,
          };
        }
      });
    },
    openDialog(row) {
      this.siteRow = row;
      this.showDialog = true;
    },
    closeHandle() {
      this.showDialog = false;
      this.siteRow = {};
    },
    successHandle() {
      this.getListHandle();
      this.closeHandle();
    },
  },
};
</script>

<style lang="scss" scoped>
.page-wapper {
  width: 100%;
  height: 100%;
  padding: 16px;
  box-sizing: border-box;
  background: #edf3fa;
}

.wapper-box {
  width: 100%;
  height: 100%;
  background-color: #fff;
  box-sizing: border-box;
  border-radius: 8px;
}
.header-btn-box {
  display: flex;
  align-items: center;
  height: 64px;
  justify-content: flex-end;
  padding: 0 24px;
}
.header-search-box {
  margin-bottom: 16px;
  padding: 0 24px;
}
.statistics-box {
  display: flex;
  align-items: center;
  height: 40px;
  background: #f0f6fc;
  margin: 0 24px 16px;
  padding: 0 16px;
  box-sizing: border-box;
}
.statistics-item {
  color: #333;
}
.statistics-num {
  color: #0d70f2;
  font-weight: bold;
}
.content-box {
  width: 100%;
  height: calc(100% - 276px);
  overflow: auto;
  padding: 0 24px;
  box-sizing: border-box;
}
.content-box::after {
  content: "";
  display: table;
  clear: both;
}
.pager-box {
  padding: 8px 24px;
}
.list-item {
  width: calc((100% - 64px) / 5);
  height: 284px;
  background: rgba(255, 255, 255, 0.9);
  border-radius: 4px;
  box-shadow: 0px 0px 8px 0px rgba(0, 0, 0, 0.1);
  padding: 8px;
  box-sizing: border-box;
  margin-right: 16px;
  margin-bottom: 16px;
  float: left;
  cursor: pointer;
}
.list-item:nth-child(5n) {
  margin-right: 0;
}
.list-image-box {
  width: 100%;
  height: 180px;
  position: relative;
}
.list-status {
  position: absolute;
  top: 0;
  right: 0;
  padding: 0 12px;
  height: 30px;
  line-height: 30px;
  border-bottom-left-radius: 8px;
  color: #fff;
}
.list-image {
  width: 100%;
  height: 180px;
}
.list-title {
  margin-top: 12px;
  color: #333;
}
.list-dec {
  color: #666;
  margin-top: 8px;
}
.list-time {
  color: #666;
  margin-top: 8px;
}
.status-Slight {
  background: #ffd600;
}
.status-Normal {
  background: #ff7b00;
}
.status-Critical {
  background: #eb110a;
}
.status-text-Slight {
  color: #ffd600;
}
.status-text-Normal {
  color: #ff7b00;
}
.status-text-Critical {
  color: #eb110a;
}
.list-item-bottom {
  display: flex;
  justify-content: space-between;
}
.bottom-left {
  width: calc(100% - 72px);
}
.bottom-left-yjc {
  width: 100%;
}
.btn-box {
  margin-top: 8px;
  margin-left: 5px;
}
.list-title {
  display: flex;
  align-items: center;
}
.status-text {
  margin-left: 5px;
  width: 58px;
  height: 24px;
  line-height: 24px;
  border-radius: 4px;
  background: #ff7b00;
  color: #fff;
  text-align: center;
  flex-shrink: 0;
}
.status-text-yjc {
  background: #2ed459;
}
.list-title-text {
}
.from-box {
  display: flex;
}
.search-btn-box {
  display: flex;
  justify-content: flex-end;
  margin-top: 16px;
}
.header-search-box {
  /deep/ .el-form-item {
    flex: 1;
  }
  /deep/ .el-form--inline .el-form-item {
    display: flex;
    margin-right: 0;
    margin-bottom: 0;
  }
  /deep/ .el-form--inline .el-form-item__content {
    flex: 1;
  }
  /deep/ .el-form-item + .el-form-item {
    margin-left: 40px;
  }
}
</style>