import {
  saveSiteExtRequest,
  updateSiteExtRequest,
} from "@/api/extend/extendData";
const saveResourceMixins = {
  data() {
    return {
      btnLoading: false,
    };
  },
  methods: {
    saveHandle() {
      this.btnLoading = true;
      this.$refs.formRef
        .validateHandle()
        .then((data) => {
          let resquest = null;
          if (this.operaType === "edit") {
            resquest = updateSiteExtRequest;
          } else if (this.operaType === "add") {
            resquest = saveSiteExtRequest;
          }
          resquest(data.node).then((res) => {
            this.btnLoading = false;
            if (res.code === 200) {
              this.$message({
                type: "success",
                message: "保存成功",
              });
              this.$emit("save-success", data);
              this.handleClose && this.handleClose();
            }
          }).catch((err) => {
            this.btnLoading = false;
          });
        })
        .catch((err) => {
          this.btnLoading = false;
        });
    },
  },
};

export { saveResourceMixins };
