<template>
  <div class="data-auth-detail-box">
    <div class="detail-box">
      <pt-data-auth-form
        ref="formRef"
        :dataAuthId="dataAuthId"
        :operaType="operaType"
        :itemData="itemData"
      ></pt-data-auth-form>
    </div>
    <div class="detail-bottom">
      <el-button type="primary" @click="saveHandle" :loading="btnLoading"
        >保存</el-button
      >
      <el-button type="danger" @click="deleteHandle">删除</el-button>
    </div>
  </div>
</template>

<script>
import PtDataAuthForm from "./PtDataAuthForm.vue";
import { saveResourceMixins } from './../common/saveDataAuthMixins'
export default {
  mixins: [saveResourceMixins],
  props: {
    dataAuthId: {
      type: String,
      default: ''
    },
    itemData: {
      type: Object,
      default() {
        return {}
      }
    }
  },
  components: {
    PtDataAuthForm
  },
  data() {
    return {
      operaType: 'edit',
      btnLoading: false
    };
  },
  methods: {
    deleteHandle() {
      this.$emit('delete')
    }
  }
};
</script>

<style lang="scss" scoped>
.data-auth-detail-box {
  height: calc(100% - 65px);
}
.detail-box {
  height: calc(100% - 64px);
  overflow: auto;
  padding: 16px 24px 8px;
  box-sizing: border-box;
}
.detail-bottom {
  height: 64px;
  display: flex;
  align-items: center;
  border-top: 1px solid #f2f5f7;
  padding: 0 16px;
}
</style>