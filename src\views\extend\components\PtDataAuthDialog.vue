<template>
  <el-dialog
    title="新增数据服务"
    :visible.sync="dialogVisible"
    :close-on-click-modal="false"
    width="640px"
    :before-close="handleClose"
  >
    <div class="dialog-box">
      <pt-data-auth-form
        ref="formRef"
        :operaType="operaType"
      ></pt-data-auth-form>
    </div>

    <span slot="footer" class="dialog-footer">
      <el-button @click="handleClose">取 消</el-button>
      <el-button type="primary" @click="saveHandle" :loading="btnLoading"
        >确 定</el-button
      >
    </span>
  </el-dialog>
</template>
<script>
  import PtDataAuthForm from "./PtDataAuthForm";
  import { saveResourceMixins } from './../common/saveDataAuthMixins'
  export default {
    name: 'PtDataAuthDialog',
    mixins: [saveResourceMixins],
    components: {
      PtDataAuthForm,
    },
    data() {
      return {
        dialogVisible: true,
        operaType: 'add'
      }
    },
    created() {
      this.dialogVisible = true
    },
    methods: {
      handleClose() {
        this.$emit("close");
      },

    }
  }
</script>
<style lang="scss" scoped>

</style>