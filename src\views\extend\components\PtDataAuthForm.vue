<template>
  <el-form :model="formData" ref="formRef" :rules="rules" label-position="top">
    <div class="form-box">
      <div class="form-item-box">
        <el-form-item label="扩展名称:" prop="name">
          <el-input v-model="formData.name" maxlength="100" placeholder="请输入扩展名称"></el-input>
        </el-form-item>

        <el-form-item label="扩展编码:" prop="code">
          <el-input v-model="formData.code" maxlength="40" placeholder="请输入扩展编码"></el-input>
        </el-form-item>
      </div>
    </div>
    <div class="form-box">
      <div class="form-item-box">
        <el-form-item label="扩展地址:" prop="url">
          <el-input v-model="formData.url" maxlength="200" placeholder="请输入扩展地址"></el-input>
        </el-form-item>
      </div>
    </div>
  </el-form>
</template>

<script>
export default {
  props: {
    dataAuthId: {
      type: String,
      default: ''
    },
    itemData: {
      type: Object,
      default() {
        return {}
      }
    }
  },
  watch: {
    itemData: {
      handler() {
        this.formData = JSON.parse(JSON.stringify(this.itemData))
      },
      immediate: true
    }
  },
  data() {
    function validateCommonCode(rule, value, callback) {
      if (!value) {
          //非空参数    
        callback();
      } else {
        let isValidate = /^[0-9a-zA-Z_]{1,}$/i.test(value);
        if (isValidate) {
          callback();
        } else {
          callback(new Error("编码只能输入字母，数字及下划线！"));
        }
      }
    }
    return {
      formData: {
        name: "",
        code: "",
        url: "",
      },
      rules: {
        name: [
          {
            required: true,
            trigger: "blur",
            message: "扩展名称不能为空",
          },
        ],
        code: [
          {
            required: true,
            trigger: "blur",
            message: "扩展编码不能为空",
          },
          {
            validator: validateCommonCode,
            trigger: 'blur'
          }
        ],
        url: [
          {
            required: true,
            message: "扩展地址不能为空",
            trigger: "blur",
          },
        ],
      },
    };
  },
  methods: {
    validateHandle() {
      let that = this;
      return new Promise((resolve, reject) => {
        this.$refs.formRef.validate((vali) => {
          if (vali) {
            resolve({
              node: that.formData,
            });
          } else {
            reject();
          }
        });
      });
    },
  }
};
</script>


<style lang="scss" scoped>
.form-box {
  display: flex;
  flex: 1;
}
.form-box-dialog {
  display: block;
  margin-left: 0;
}
.form-item-box {
  display: flex;
  flex: 1;
}
.form-item-box + .form-item-box {
  margin-left: 48px;
}
.form-box-dialog .form-item-box + .form-item-box {
  margin-left: 0;
}
.form-item-box /deep/ .el-form-item {
  flex: 1;
}
.form-item-box .el-form-item + .el-form-item {
  margin-left: 48px;
}
.form-box-base {
  display: flex;
}
.form-box-base .form-box + .form-box {
  margin-left: 48px;
}
</style>