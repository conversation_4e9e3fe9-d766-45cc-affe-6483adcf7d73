<template>
  <div class="data-auth-list-box">
    <div class="content-box" ref="contentRef">
      <el-table
        ref="table"
        v-loading="loading"
        element-loading-text="数据加载中..."
        element-loading-spinner="el-icon-loading"
        element-loading-background="rgba(0, 0, 0, 0.8)"
        border
        stripe
        style="width: 100%"
        :max-height="tableHeight"
        tooltip-effect="light"
        :data="tableData"
        row-key="id"
        @selection-change="handleSelectionChange"
      >
        <el-table-column type="selection" width="55" reserve-selection>
        </el-table-column>
        <el-table-column label="序号" align="center" width="70">
          <template slot-scope="scope">
            <span>
              {{ (searchItem.pageNum - 1) * searchItem.pageSize + scope.$index + 1 }}
            </span>
          </template>
        </el-table-column>
        <el-table-column
          prop="name"
          label="监控点名称"
          align="left"
          show-overflow-tooltip
        >
          <template slot-scope="scope">
            {{ scope.row.name }}
          </template>
        </el-table-column>
        <el-table-column
          prop="videoServerName"
          label="视频服务器"
          show-overflow-tooltip
        >
        </el-table-column>
        <el-table-column prop="adcdName" label="行政区划" show-overflow-tooltip>
        </el-table-column>
        <el-table-column
          prop="cameraTypeName"
          label="监控点类型"
          show-overflow-tooltip
        >
        </el-table-column>
        <el-table-column
          prop="transTypeName"
          label="拉流方式"
          show-overflow-tooltip
        >
        </el-table-column>

        <el-table-column label="操作" align="right" fixed="right" width="160">
          <template slot-scope="scope">
            <span @click="handelDelete('single', scope.row)" class="oper-btn">
              解除绑定
            </span>
          </template>
        </el-table-column>
      </el-table>
    </div>

    <div class="pager-box" ref="pager">
      <el-pagination
        @size-change="handleSizeChange"
        @current-change="handleCurrentChange"
        :current-page="searchItem.pageNum"
        :page-sizes="pageSizes"
        :page-size="searchItem.pageSize"
        layout="total, ->, prev, pager,next, sizes, jumper"
        :total="total"
      >
      </el-pagination>
    </div>
    <data-auth-user-dialog
      v-if="showDialog"
      :dataId="dataId"
      :bindUsers="bindUsers"
      @close="closeHandle"
      @save="saveHandle"
    ></data-auth-user-dialog>
  </div>
</template>

<script>
import {
  getSiteExtListRequest,
  getSiteExtPageRequest,
  bindByDataIdExtRequest,
  unbindExtRequest
} from "@/api/extend/extendData";
import dataAuthUserDialog from "./dataAuthUserDialog.vue";
export default {
  components: {
    dataAuthUserDialog,
  },
  props: {
    dataId: {
      type: String,
      default: "",
    },
    itemData: {
      type: Object,
      default() {
        return {};
      },
    },
  },
  watch: {
    dataId: {
      handler() {
        if (this.dataId) {
          this.searchItem.dataIds = [this.dataId];
          this.searchItem.extCode = this.itemData && this.itemData.serverCode;
          // this.searchItem.videoServerId = this.itemData && this.itemData.id
        }
        this.handleSearch();
        this.$refs.table && this.$refs.table.clearSelection();
      },
      immediate: true,
    },
  },
  data() {
    return {
      showDialog: false,
      searchItem: {
        pageSize: 20,
        pageNum: 1,
        name: "",
        videoServerId: "",
        cameraType: "",
        adcd: "",
        serverType: "",
        extCode: "",
        dataType: "",
        dataId: "",
        dataIds: [],
      },
      tableHeight: 500,
      tableData: [],
      selectUsers: [],
      pageSizes: [10, 20, 30, 40, 50],
      total: 0,
      loading: false,
      bindUsers: [],
      deleteUsers: [],
    };
  },
  mounted() {
    this.$nextTick(() => {
      this.initHandle()
    })
  },
  methods: {
    initHandle() {
      this.tableHeight = this.$refs.contentRef.clientHeight
    },
    addHandle() {
      this.showDialog = true;
    },
    searchHandle(data) {
      this.searchItem.name = data.name;
      this.searchItem.videoServerId = data.videoServerId;
      this.searchItem.cameraType = data.cameraType;
      this.handleSearch();
    },
    handleSearch() {
      this.searchItem.pageNum = 1;
      this.getListHandle();
    },
    handleSizeChange(val) {
      this.searchItem.pageSize = val;
      this.handleSearch();
    },
    handleCurrentChange(val) {
      this.searchItem.pageNum = val;
      this.getListHandle();
    },
    getListHandle() {
      if (this.searchItem.dataIds.length === 0) {
        this.tableData = [];
        this.total = 0;
        return;
      }
      getSiteExtPageRequest(this.searchItem).then((res) => {
        if (res.code === 200) {
          this.tableData = res.data.records;
          this.total = res.data.total;
          this.getAllListHandle();
        }
      });
    },
    getAllListHandle() {
      getSiteExtListRequest(this.searchItem).then((res) => {
        if (res.code === 200) {
          this.bindUsers = res.data;
        }
      });
    },
    handleSelectionChange(rows) {
      this.selectUsers = rows;
    },
    closeHandle() {
      this.showDialog = false;
    },
    handelDelete(type, row) {
      let that = this;
      if (type === "multiple") {
        if (this.selectUsers.length === 0) {
          this.$message({
            type: "warning",
            message: "请至少选择一条数据",
          });
          return;
        }
        that.deleteUsers = this.selectUsers.map((d) => d.extId);
      } else {
        that.deleteUsers = [row.extId];
      }
      that
        .$confirm("此操作将解除该监控点的绑定, 是否继续?", "提示", {
          confirmButtonText: "确定",
          cancelButtonText: "取消",
          type: "warning",
        })
        .then(() => {
          unbindExtRequest(this.deleteUsers).then((res) => {
            if (res.code === 200) {
              this.$message({
                type: "success",
                message: "解绑成功",
              });
              that.handleSearch();
              if (type === "multiple") {
                that.$refs.table.clearSelection();
              }
            }
          });
        })
        .catch((err) => {});
    },
    saveHandle(data) {
      let params = data.map((d) => {
        return {
          dataId: this.dataId,
          siteId: d.id,
          extCode: this.itemData.serverCode,
          dataType: this.itemData.dataType,
          dataPid: this.itemData.dataPid,
          dataName: this.itemData.dataName,
        };
      });
      bindByDataIdExtRequest(params).then((res) => {
        if (res.code === 200) {
          this.$message({
            type: "success",
            message: "保存成功",
          });
          this.closeHandle();
          this.handleSearch();
        }
      });
    },
  },
};
</script>


<style lang="scss" scoped>
.data-auth-list-box {
  height: calc(100% - 65px);
  padding: 0 16px;
}
.content-box {
  width: 100%;
  height: calc(100% - 48px);
  overflow: auto;
}
.pager-box {
  padding: 8px 0 0;
}
</style>
<style scoped>
.el-table .el-table__body-wrapper {
  width: 100% !important;
}
</style>