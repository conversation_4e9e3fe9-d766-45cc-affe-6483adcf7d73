<template>
  <div class="tool-box" :class="{ 'tool-box-active': dataType === 2 }">
    <div class="tool-btn-box">
      <el-button type="primary" @click="addHandle" icon="el-icon-plus"
        >新增扩展服务</el-button
      >
      <el-button
        :disabled="!disabled"
        v-if="dataType === 2"
        type="primary"
        @click="addAuthHandle"
        icon="el-icon-plus"
        >新增监控点绑定</el-button
      >
      <el-button v-if="dataType === 2" @click="deleteHandle"
        >批量解除绑定</el-button
      >
    </div>
    <el-form
      :model="searchItem"
      ref="formRef"
      :inline="true"
      v-if="dataType === 2"
    >
      <el-form-item label="服务器:">
        <el-select
          v-model="searchItem.videoServerId"
          placeholder="请选择服务器"
          clearable
        >
          <el-option
            v-for="item in serverOptions"
            :key="item.id"
            :label="item.name"
            :value="item.id"
          >
          </el-option>
        </el-select>
      </el-form-item>
      <el-form-item label="监控点类型:">
        <pt-dict-down-list
          :selectItem="searchItem.cameraType"
          :type="dictCodes.cameraType"
          :addAll="true"
          @change="typeOptionsChange"
        ></pt-dict-down-list>
      </el-form-item>
      <el-input
        placeholder="请输入关键词"
        v-model="searchItem.name"
        class="input-with-select"
        @keyup.enter.native="searchHandle"
      >
        <el-button
          slot="append"
          type="primary"
          icon="el-icon-search"
          @click="searchHandle"
        ></el-button>
      </el-input>
    </el-form>
  </div>
</template>
<script>
import PtDictDownList from "@/components/PtDictDownList/index.vue";
import { QueryServerListRequest } from "@/api/server/serverData";
export default {
  components: {
    PtDictDownList
  },
  props: {
    dataType: {
      type: [Number, String],
      default: 1,
    },
    disabled: {
      type: Boolean,
      default: false,
    },
  },
  data() {
    return {
      searchItem: {
        videoServerId: '',
        cameraType: '',
        name: ''
      },
      serverOptions: []
    };
  },
  mounted() {
    this.getServerData()
  },
  methods: {
    getServerData() {
      QueryServerListRequest("").then((res) => {
        if (res.code === 200) {
          this.serverOptions = res.data;
        }
      });
    },
    typeOptionsChange(value) {
      this.searchItem.cameraType = value
    },
    addHandle() {
      this.$emit("add");
    },
    addAuthHandle() {
      this.$emit("add-auth");
    },
    searchHandle() {
      this.$emit("search", this.searchItem);
    },
    deleteHandle() {
      this.$emit("batch-delete");
    },
  },
};
</script>
<style lang="scss" scoped>
.tool-box {
  height: 64px;
  display: flex;
  align-items: center;
  padding: 0 16px;
  border-bottom: 1px solid #f2f5f7;
  justify-content: space-between;
}
.tool-box-active {
  border: none;
}
.input-with-select {
  width: 300px;
}
/deep/ .el-form-item {
  margin-bottom: 0
}
</style>