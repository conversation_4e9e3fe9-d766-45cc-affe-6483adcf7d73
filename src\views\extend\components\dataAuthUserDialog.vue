<template>
  <el-dialog
    :title="title"
    :visible.sync="dialogVisible"
    :close-on-click-modal="false"
    width="1080px"
    :before-close="handleClose"
  >
    <div class="dialog-box">
      <div class="dialog-right">
        <div class="dialog-right-search">
          <el-form :model="searchItem" ref="formRef" :inline="true">
            <el-form-item label="服务器:">
              <el-select
                v-model="searchItem.videoServerId"
                placeholder="请选择服务器"
                clearable
              >
                <el-option
                  v-for="item in serverOptions"
                  :key="item.id"
                  :label="item.name"
                  :value="item.id"
                >
                </el-option>
              </el-select>
            </el-form-item>
            <el-form-item label="监控点类型:">
              <pt-dict-down-list
                :selectItem="searchItem.cameraType"
                :type="dictCodes.cameraType"
                :addAll="true"
                @change="typeOptionsChange"
              ></pt-dict-down-list>
            </el-form-item>
            <el-input
              placeholder="请输入关键词"
              v-model="searchItem.name"
              class="input-with-select"
              @keyup.enter.native="handleSearch"
            >
              <el-button
                slot="append"
                type="primary"
                icon="el-icon-search"
                @click="handleSearch"
              ></el-button>
            </el-input>
          </el-form>
        </div>
        <div class="content-box" ref="contentRef">
          <el-table
            ref="table"
            v-loading="loading"
            element-loading-text="数据加载中..."
            element-loading-spinner="el-icon-loading"
            element-loading-background="rgba(0, 0, 0, 0.8)"
            border
            stripe
            style="width: 100%"
            tooltip-effect="light"
            :data="tableData"
            :max-height="tableHeight"
            row-key="id"
            @selection-change="handleSelectionChange"
          >
            <el-table-column type="selection" width="55" reserve-selection>
            </el-table-column>
            <el-table-column
              label="序号"
              width="70"
              type="index"
              align="center"
              show-overflow-tooltip
            >
              <template slot-scope="scope">
                {{ (currentPage - 1) * pageSize + scope.$index + 1 }}
              </template>
            </el-table-column>

            <el-table-column
              prop="name"
              label="监控点名称"
              align="left"
              show-overflow-tooltip
            >
              <template slot-scope="scope">
                {{ scope.row.name }}
              </template>
            </el-table-column>
            <el-table-column
              prop="videoServerName"
              label="视频服务器"
              show-overflow-tooltip
            >
            </el-table-column>
            <el-table-column
              prop="adcdName"
              label="行政区划"
              show-overflow-tooltip
            >
            </el-table-column>
            <el-table-column
              prop="cameraTypeName"
              label="监控点类型"
              show-overflow-tooltip
            >
            </el-table-column>
            <el-table-column
              prop="transTypeName"
              label="拉流方式"
              show-overflow-tooltip
            >
            </el-table-column>
          </el-table>
        </div>
        <div class="pager-box" ref="pager">
          <el-pagination
            @size-change="handleSizeChange"
            @current-change="handleCurrentChange"
            :current-page="currentPage"
            :page-sizes="pageSizes"
            :page-size="pageSize"
            layout="total, ->, prev, pager,next, sizes, jumper"
            :total="total"
          >
          </el-pagination>
        </div>
      </div>
    </div>
    <span slot="footer" class="dialog-footer">
      <el-button @click="handleClose">取 消</el-button>
      <el-button type="primary" @click="saveHandle" :btnLoading="btnLoading"
        >确 定</el-button
      >
    </span>
  </el-dialog>
</template>

<script>
import { PageSiteRequest } from "@/api/site/siteData";
import { convertTableData } from "@/views/monPointsCfg/components/siteTableUtil";
import PtDictDownList from "@/components/PtDictDownList/index.vue";
import { QueryServerListRequest } from "@/api/server/serverData";
export default {
  components: {
    PtDictDownList,
  },
  props: {
    dataId: {
      type: String,
      default: "",
    },
    bindUsers: {
      type: Array,
      default: () => [],
    },
  },
  data() {
    return {
      dialogVisible: true,
      btnLoading: false,
      title: "绑定监控点",
      currentItem: {},
      menuActiveId: "",

      searchItem: {
        adcd: "",
        cameraType: "",
        name: "",
        videoServerId: "",
      },

      loading: false,
      tableData: [],
      total: 0,
      currentPage: 1,
      pageSize: 20,
      pageSizes: [10, 20, 30, 40, 50],
      selectUsers: [],
      otherUsers: [],

      tableHeight: 200,
      serverOptions: []
    };
  },
  mounted() {
    this.dialogVisible = true;
    this.initHandle();
    this.getServerData();
    this.handleSearch();
    this.otherUsers = JSON.parse(JSON.stringify(this.bindUsers));
  },
  methods: {
    initHandle() {
      this.$nextTick(() => {
        this.tableHeight = this.$refs.contentRef.clientHeight;
      });
    },
    handleClose() {
      this.$emit("close");
    },
    getServerData() {
      QueryServerListRequest("").then((res) => {
        if (res.code === 200) {
          this.serverOptions = res.data;
        }
      });
    },
    typeOptionsChange(value) {
      this.searchItem.cameraType = value;
    },
    saveHandle() {
      let selectUsers = this.selectUsers.concat(this.otherUsers);
      if (selectUsers.length > 0) {
        this.$emit("save", selectUsers);
      } else {
        this.$message({
          type: "warning",
          message: "请至少选择一条数据",
        });
      }
    },
    handleSearch() {
      this.currentPage = 1;
      this.getListHandle();
    },
    handleSizeChange(val) {
      this.pageSize = val;
      this.handleSearch();
    },
    handleCurrentChange(val) {
      this.currentPage = val;
      this.getListHandle();
    },
    getListHandle() {
      let queryObject = {
        ...this.searchItem,
        pageNum: this.currentPage,
        pageSize: this.pageSize,
      };
      this.loading = true;
      PageSiteRequest(queryObject)
        .then((response) => {
          this.loading = false;
          if (response.code === 200) {
            let tableData = response.data.records;
            this.total = response.data.total;
            convertTableData(tableData).then((newData) => {
              this.tableData = newData;
              this.setSelectUsers();
            });
          } else {
            this.$message({
              message: response.desc,
              type: "error",
            });
          }
        })
        .catch((err) => {
          this.loading = false;
        });
    },
    setSelectUsers() {
      if (this.otherUsers.length > 0) {
        this.tableData.forEach((row) => {
          let index = this.otherUsers.findIndex((d) => d.id === row.id);
          if (index > -1) {
            this.$refs.table.toggleRowSelection(row, true);
            this.otherUsers.splice(index, 1);
          }
        });
      }
    },
    handleSelectionChange(row) {
      this.selectUsers = row;
    },
  },
};
</script>


<style lang="scss" scoped>
.dialog-box {
  height: 70vh;
  display: flex;
  overflow: hidden;
}
.dialog-right {
  width: 100%;
  height: calc(100% - 36px);
  box-sizing: border-box;
}
.dialog-right-search {
  display: flex;
  justify-content: flex-end;
  margin-bottom: 16px;
}
.content-box {
  width: 100%;
  height: calc(100% - 50px);
  overflow: auto;
  box-sizing: border-box;
}
.pager-box {
  padding: 8px 16px;
}
.input-with-select {
  width: 300px;
}
/deep/ .el-form-item {
  margin-bottom: 0
}
</style>
