<template>
  <div class="page-wapper">
    <div class="page-box">
      <pt-nav-menu-tree
        ref="menuTreeRef"
        title="监控点扩展管理"
        :lazy="false"
        :treeData="treeData"
        node-key="dataId"
        :tree-props="menuTreeOptions"
        :expandedKeys="expandedKeys"
        :currentNodeKey="menuActiveId"
        @menu-item-click="menuItemClick"
      ></pt-nav-menu-tree>
      <div class="page-right-box">
        <pt-data-auth-tool
          @add="addHandle"
          @add-auth="addAuthHandle"
          @batch-delete="batchDeleteHandle"
          @search="searchHandle"
          :dataType="dataType"
          :disabled="menuActiveData.selective"
        ></pt-data-auth-tool>
        <template v-if="dataType === 1">
          <pt-data-auth-detail
            :itemData="menuActiveData"
            @save-success="saveSuccessHandle"
            @delete="deleteHandle"
          ></pt-data-auth-detail>
        </template>
        <pt-data-auth-list
          ref="authListRef"
          v-else
          :dataId="menuActiveId"
          :itemData="menuActiveData"
        ></pt-data-auth-list>
      </div>
    </div>
    <pt-data-auth-dialog
      v-if="showDialog"
      @close="closeHandle"
      @save-success="saveSuccessHandle"
    ></pt-data-auth-dialog>
  </div>
</template>

<script>
import PtNavMenuTree from "@/components/PtNavMenuTree/index";
import PtDataAuthTool from "./components/PtDataAuthTool.vue";
import PtDataAuthDetail from "./components/PtDataAuthDetail.vue";
import PtDataAuthList from "./components/PtDataAuthList.vue";
import PtDataAuthDialog from "./components/PtDataAuthDialog.vue";
import { getDataAuthData } from '@/utils/index'
import {
  getExtentConfListRequest,
  deleteExtConfRequest
} from '@/api/extend/extendData'
export default {
  name: "dataAuth",
  components: {
    PtNavMenuTree,
    PtDataAuthTool,
    PtDataAuthDetail,
    PtDataAuthList,
    PtDataAuthDialog,
  },
  data() {
    return {
      showDialog: false,
      treeData: [],
      menuTreeOptions: {
        label: "dataName",
        children: "children",
      },
      expandedKeys: [],
      menuActiveId: "",
      menuActiveData: {
        selective: true
      },
      dataType: 2,
      serviceCode: '',
    };
  },
  created() {
    this.getConfData();
  },
  methods: {
    getConfData() {
      let that = this
      getExtentConfListRequest().then((res) => {
        if (res.code === 200) {
          let data = res.data;
          getDataAuthData(data).then(response => {
            that.treeData = response
            that.getMenuTreeFirstLeave(that.treeData);
          });
        }
      });
    },
    getMenuTreeFirstLeave(data) {
      if (data.length > 0) {
        this.expandedKeys.push(data[0].dataId);
        if (data[0] && data[0].children) {
          this.getMenuTreeFirstLeave(data[0].children);
        } else {
          if (this.menuActiveId) return;
          let node = data[0] || {};
          this.menuActiveId = node.dataId;
          this.menuActiveData = node;
          if (node.code) {
            this.dataType = 1;
          } else {
            this.dataType = 2;
          }
        }
      }
    },
    menuItemClick(node) {
      this.menuActiveId = node.dataId;
      this.menuActiveData = node;
      if (node.code) {
        this.dataType = 1;
      } else {
        this.dataType = 2;
      }
    },
    addHandle() {
      this.showDialog = true;
    },
    addAuthHandle() {
      this.$refs.authListRef && this.$refs.authListRef.addHandle();
    },
    closeHandle() {
      this.showDialog = false;
    },
    saveSuccessHandle() {
      this.getConfData();
    },
    batchDeleteHandle() {
      this.$refs.authListRef && this.$refs.authListRef.handelDelete('multiple')
    },
    searchHandle(data) {
      this.$refs.authListRef && this.$refs.authListRef.searchHandle(data)
    },
    deleteHandle() {
      this.$confirm("此操作将永久删除该数据, 并且会解绑这个服务下的所有数据, 是否继续?", "提示", {
        confirmButtonText: "确定",
        cancelButtonText: "取消",
        type: "warning",
      })
        .then(() => {
          deleteExtConfRequest(this.menuActiveData.id).then((res) => {
            if (res.code === 200) {
              this.$message({
                type: "success",
                message: "删除成功!",
              });
              this.menuActiveId = "";
              this.menuActiveData = {};
              this.getConfData();
            } else {
              this.$message({
                type: "error",
                message: "删除失败!",
              });
            }
          });
        })
        .catch(() => {});
    },
  },
};
</script>

<style lang="scss" scoped>

.page-wapper {
  width: 100%;
  height: 100%;
  padding: 16px;
  box-sizing: border-box;
  background: #EDF3FA;
}
.wapper-box {
  width: 100%;
  height: 100%;
  background-color: #fff;
  box-sizing: border-box;
  border-radius: 8px;
}
.page-box {
  height: 100%;
  display: flex;
}



.page-right-box {
  width: calc(100% - 316px);
  height: 100%;
  background: #fff;
  border-radius: 8px;
  box-sizing: border-box;
}
</style>