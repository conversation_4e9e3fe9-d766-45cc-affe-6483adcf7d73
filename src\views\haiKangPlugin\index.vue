<template>
  <div
    class="page-wrapper"
    :class="{ 'page-wrapper-dark': pageTheme === 'dark' }"
  >
    <div class="wrapper-left">
      <div
        v-if="!subCode"
        class="card-wrapper catalog-wrapper"
        :class="{
          'catalog-wrapper-record': activeName === 'playback',
          'catalog-wrapper-active': toggleRight,
        }"
      >
        <el-tabs v-model="activeTag" @tab-click="changeTag">
          <el-tab-pane
            v-for="(tag, index) in tagData"
            :key="index"
            :label="tag.name"
            :name="tag.id"
          ></el-tab-pane>
        </el-tabs>
        <div class="card-wrapper-main">
          <div class="search-box">
            <el-input
              placeholder="请输入关键字"
              v-model="keyword"
              @keyup.enter.native="searchHandle"
            >
              <el-button
                slot="append"
                icon="el-icon-search"
                @click="searchHandle"
              >
              </el-button>
            </el-input>
          </div>
          <div class="tree-box">
            <el-tree
              ref="tree"
              class="custom-tree"
              :data="catalogTree"
              node-key="id"
              default-expand-all
              highlight-current
              :filter-node-method="filterNode"
              :expand-on-click-node="false"
              @node-click="clickTreeNode"
            >
              <div
                class="custom-tree-node"
                slot-scope="{ node, data }"
                @dblclick="dblclickHandle(data)"
              >
                <div class="custom-tree-box" :title="data.name">
                  <div class="custom-tree-node-label-box">
                    <img
                      v-if="data.inline === '1' && data.cameraType"
                      :src="
                        require('@/assets/images/icon/icon-' +
                          iconObject[data.cameraType] +
                          '-active.png')
                      "
                    />
                    <img
                      v-if="data.inline === '0' && data.cameraType"
                      :src="
                        require('@/assets/images/icon/icon-' +
                          iconObject[data.cameraType] +
                          '.png')
                      "
                    />
                    <div class="custom-tree-node-label text-limit1">
                      {{ data.name }}
                    </div>
                  </div>
                  <img
                    src="@/assets/images/icon/icon-collect-active.png"
                    class="icon-ai"
                    alt=""
                    srcset=""
                    title="已收藏"
                    @click.stop="collectHandle(data, false)"
                    v-if="data.collected && data.cameraIndexCode"
                  />
                  <img
                    src="@/assets/images/icon/icon-collect.png"
                    class="icon-ai"
                    alt=""
                    srcset=""
                    title="收藏"
                    @click.stop="collectHandle(data, true)"
                    v-else-if="data.cameraIndexCode"
                  />
                </div>
              </div>
            </el-tree>
          </div>
        </div>
      </div>
      <div
        v-else
        class="card-wrapper catalog-wrapper"
        :class="{ 'catalog-wrapper-record': activeName === 'playback' }"
      >
        <leftBlock ref="tree" @leftdblclick="dblclickHandle"></leftBlock>
      </div>
      <div
        class="left-bottom-box"
        :class="{
          'left-bottom-box-record': activeName === 'playback',
          'left-bottom-box-active': toggleRight,
        }"
      >
        <div class="left-bottom-header" @click="handleRightClick">
          <span class="left-bottom-header-title">{{
            activeName === "preview" ? "云台控制" : "录像查询"
          }}</span>
          <i
            class="el-icon-arrow-right"
            :class="{ 'is-active': !toggleRight }"
          ></i>
        </div>
        <div class="bottom-box" :class="{ 'bottom-box-active': toggleRight }">
          <real-tool
            ref="realToolRef"
            :pageTheme="pageTheme"
            @control="controlHandle"
            @gotoPreset="gotoPresetHandle"
            v-if="activeName === 'preview'"
          ></real-tool>
          <record-tool
            @search="recordSearchHandle"
            :pageTheme="pageTheme"
            @change="changeRecordHandle"
            v-if="activeName === 'playback'"
          ></record-tool>
        </div>
      </div>
    </div>
    <div class="wrapper-right">
      <div class="wrapper-right-main card-wrapper">
        <el-tabs v-model="activeName" @tab-click="handleClick">
          <el-tab-pane label="实时预览" name="preview"></el-tab-pane>
          <el-tab-pane label="视频回放" name="playback"></el-tab-pane>
        </el-tabs>
        <!-- <el-button
            icon="el-icon-close"
            @click="closeAll"
            v-if="activeName === 'preview'"
            class="icon-close-all"
            >关闭所有视频</el-button
          > -->
        <div class="wrapper-right-box">
          <hk-plugin-view
            ref="hkPluginView"
            :pageTheme="pageTheme"
            :keyIndex="1"
            :cameraIndexCode="previewData.cameraIndexCode"
            :viewType="activeName"
            @changeIndex="changeCameraIndexCodeHandle"
            @isNotInstall="isNotInstallHandle"
            @selectCamera="selectCameraHandle"
          ></hk-plugin-view>
          <div class="not-install-box" v-if="notInstall">
            <el-empty
              description="您好，暂未安装插件，安装海康插件，才能使用"
            ></el-empty>
            <div class="no-install-btn-box">
              <el-button @click="downHandle"> 下载插件 </el-button>
              <el-button @click="refreshHandle"> 已安装，刷新页面 </el-button>
            </div>
          </div>
          <!-- <real-preview
              ref="previewRef"
              :cameraIndexCode="previewData.cameraIndexCode"
              :isPlugins="isPlugins.AI"
              v-if="activeName === 'preview'"
              :pageTheme="pageTheme"
              @changeIndex="changePreviewIndex"
              @collect="collectHandle"
            ></real-preview>
            <record-preview
              ref="recordRef"
               :pageTheme="pageTheme"
              v-if="activeName === 'playback'"
            ></record-preview> -->
        </div>
      </div>
      <!-- <div class="wrapper-right-bottom card-wrapper"></div> -->
    </div>
  </div>
</template>
  
  <script>
import { ListTagRequest } from "@/api/tag/tagData";
import {
  listTreeByTagIdRequest,
  inokeHaiKangRequest,
  inokeDaHuaRequest,
} from "@/api/play/playData";
import { listStatusRequest } from "@/api/stats/statsData";
import { pluginsCommonRequest } from "@/api/common/commonData";
import { PageSiteRequest, collectSiteRequest } from "@/api/site/siteData";

import leftBlock from "@/views/videoView/components/leftBlock.vue";
import realTool from "@/views/videoView/components/realTool.vue";
import recordTool from "@/views/videoView/components/recordTool.vue";
import realPreview from "@/views/videoView/components/realPreview.vue";
import recordPreview from "@/views/videoView/components/recordPreview.vue";
import hkPluginView from "@/components/hKPluginView/index.vue";

import { formatTree, findItemByCode } from "@/utils/index";
import CameraControllerFactory from "@/utils/plugins/controlCamera";

export default {
  name: "videoView",
  components: {
    realTool,
    realPreview,
    recordPreview,
    recordTool,
    leftBlock,
    hkPluginView,
  },
  data() {
    return {
      tagData: [],
      activeTag: "",
      activeCatalog: "",
      catalogTreeLoading: false,

      catalogTree: [],
      keyword: "",
      siteData: [],
      siteList: [],
      bottomTitle: "云台控制",

      activeName: "preview",
      previewData: {
        cameraIndexCode: "",
      },
      iconObject: {
        0: "qiangji",
        1: "banqiu",
        2: "kuaiqiu",
        3: "yuntai",
        4: "qiuji",
        5: "caijishuru",
      },
      isPlugins: {
        AI: false,
      },
      doubleClickOpenAllVideo: window.DSE.doubleClickOpenAllVideo || false,
      doubleClickOpenVideo: window.DSE.doubleClickOpenVideo || false,
      collectData: [],
      pageTheme: "light",
      subCode: "",
      toggleRight: true,
      notInstall: false,
      recordForm: {
        startTime: "",
        endTime: "",
      },
    };
  },
  mounted() {
    this.subCode = this.$route.query.subCode;
    this.pageTheme = this.$route.query.pageTheme || "light";
    if (!this.subCode) {
      this.getTagDataHandle();
      this.getPluginsData();
    }
  },
  methods: {
    getPluginsData() {
      pluginsCommonRequest().then((res) => {
        if (res.code === 200) {
          this.isPlugins = res.data || { AI: false };
        }
      });
    },
    getTagDataHandle() {
      ListTagRequest().then((res) => {
        if (res.code === 200) {
          this.tagData = res.data;
          if (this.tagData.length > 0) {
            this.activeTag = this.tagData[0].id;
            this.getCatalogTree();
          }
          this.tagData.unshift({
            id: "collect",
            name: "收藏夹",
          });
        }
      });
    },
    getCollectHandle() {
      PageSiteRequest({
        pageNum: 1,
        pageSize: 1000,
        collected: true,
      }).then((res) => {
        if (res.code === 200) {
          let data = res.data;
          // this.catalogTree = data.records;
          this.siteList = data.records;
          this.getSiteStatusHandle(data.records);
        }
      });
    },
    getCatalogTree() {
      this.catalogTreeLoading = true;
      listTreeByTagIdRequest(this.activeTag)
        .then((res) => {
          this.catalogTreeLoading = false;
          if (res.code === 200) {
            let arr = res.data;
            this.initCatalogTree(arr);
            this.getSiteStatusHandle(arr);
            //
          }
        })
        .catch((err) => {
          console.error(err);
          this.catalogTreeLoading = false;
        });
    },
    //格式化树
    initCatalogTree(data) {
      if (Array.isArray(data)) {
        for (let item of data) {
          if (item.children && item.children.length > 0) {
            this.initCatalogTree(item.children);
          }
          item.children = item.children || [];
          item.siteList = item.siteList || [];
          item.loading = false;
          item.children = [...item.children, ...item.siteList];
          this.siteList = [...this.siteList, ...item.siteList];
        }
      }
    },
    getSiteStatusHandle(arr) {
      let params = this.siteList.map((d) => d.id);
      listStatusRequest(params)
        .then((res) => {
          let statusList = [];
          if (res.code === 200) {
            statusList = res.data;
            formatTree(arr, statusList);
            this.catalogTree = arr;
          } else {
            this.catalogTree = arr;
          }
        })
        .catch(() => {
          this.catalogTree = arr;
        });
    },
    filterNode(value, data) {
      if (!value) return true;
      return data.name.indexOf(value) !== -1;
    },
    searchHandle() {
      this.$refs.tree.filter(this.keyword);
    },
    clickTreeNode(node) {
      if (this.doubleClickOpenVideo) {
        return;
      }
      this.openVideoHandle(node);
    },
    openVideoHandle(node) {
      this.previewData = node;
      // 必须为海康摄像头
      if (node.cameraIndexCode && node.jrtype === "1") {
        if (!node.laoding) {
          node.laoding = true;
          if (this.activeName === "preview") {
            this.$refs.hkPluginView.startPreview(node.cameraIndexCode);
            this.changePreviewIndex(0, node);
          } else {
            this.$refs.hkPluginView.playBackHandle(
              node.cameraIndexCode,
              this.recordForm.startTime,
              this.recordForm.endTime,
              node.recordLocation
            );
          }
        }
        setTimeout(() => {
          node.laoding = false;
        }, 1000);
      } else {
        this.$message({
          type: "warning",
          message: "请选择海康摄像头",
        });
      }
    },
    changeCameraIndexCodeHandle(cameraIndexCode) {},
    controlHandle(command, action, speed) {
      // this.$refs.previewRef.controlHandle(command, action, speed);
      if (this.previewData.jrtype !== "1") {
        this.$message({
          type: "warning",
          message: "请选择海康摄像头",
        });
      } else {
        const haikangController =
          CameraControllerFactory.createController("haiKang");
        haikangController.controlCamera(
          command,
          action,
          speed,
          this.previewData.cameraIndexCode
        );
      }
    },
    changePreviewIndex(index, node) {
      this.$refs.realToolRef && this.$refs.realToolRef.getPresetHandle(node);
    },
    gotoPresetHandle(node) {
      const controller = CameraControllerFactory.createController("haiKang");
      controller.gotoPreset(node, this.$message);
    },
    changeTag() {
      this.keyword = "";
      if (this.activeTag === "collect") {
        // this.catalogTree = [];
        this.getCollectHandle();
      } else {
        this.getCatalogTree();
      }
    },
    // collectHandle() {
    //   if (this.activeTag === "collect") {
    //     this.getCollectHandle();
    //   }
    // },
    handleClick() {
      this.$nextTick(() => {
        this.$refs.hkPluginView.initPlugin();
      });
    },
    dblclickHandle(node) {
      if (this.doubleClickOpenAllVideo && !node.cameraIndexCode) {
        let videoList = this.getChildrenVideo(node);
        videoList = videoList.filter((item) => item.jrtype === "1");
        if (videoList.length > 0) {
          //   this.$refs.previewRef.openAllVideo(videoList);
          this.$refs.hkPluginView.batchStartPreview(videoList);
        }
      }
      if (this.doubleClickOpenVideo && node.cameraIndexCode) {
        this.openVideoHandle(node);
      }
    },
    // 获取所有的子节点视频
    getChildrenVideo(node) {
      let arr = [];
      if (node.children && node.children.length > 0) {
        for (let item of node.children) {
          if (item.cameraIndexCode) {
            arr.push(item);
          } else {
            arr = [...arr, ...this.getChildrenVideo(item)];
          }
        }
      }
      return arr;
    },
    closeAll() {
      this.$refs.previewRef.closeAllVideo();
    },
    changeRecordHandle(data) {
      this.recordForm.startTime = data.startTime;
      this.recordForm.endTime = data.endTime;
    },
    recordSearchHandle(item) {
      if (this.previewData.jrtype === "1") {
        this.$refs.hkPluginView.playBackHandle(
          this.previewData.cameraIndexCode,
          item.startTime,
          item.endTime,
          this.previewData.recordLocation
        );
        this.recordForm.startTime = item.startTime;
        this.recordForm.endTime = item.endTime;
      } else {
        this.$message({
          type: "warning",
          message: "请选择海康摄像头",
        });
      }
    },
    handleRightClick() {
      this.toggleRight = !this.toggleRight;
    },
    isNotInstallHandle() {
      this.notInstall = true;
    },
    downHandle() {
      window.location.href = window.DSE.haiKangPluginDownUrl;
    },
    refreshHandle() {
      this.$refs.hkPluginView.initPlugin();
    },

    selectCameraHandle(cameraIndexCode) {
      // 视频选择的窗口
      const selectNode = findItemByCode(this.catalogTree, cameraIndexCode);
      if(selectNode && this.activeName === 'preview') {
        this.$refs.tree.setCurrentKey(selectNode.id)
        this.previewData = selectNode;
      }
    },
    collectHandle(item, bool) {
      collectSiteRequest(item.id)
        .then((res) => {
          if (res.code === 200) {
            item.collected = bool;
            this.$message({
              type: "success",
              message: bool ? "收藏成功" : "取消收藏成功",
            });
          }
          if (this.activeTag === "collect") {
            this.getCollectHandle();
          }
        })
        .catch((err) => {
          this.$message({
            type: "warning",
            message: "操作失败",
          });
        });
    },
  },
};
</script>
  
  <style lang="scss" scoped>
.page-wrapper {
  display: flex;
  .wrapper-left {
    width: 300px;
    height: 100%;
  }
  .wrapper-right {
    width: calc(100% - 316px);
    display: flex;
    justify-content: space-between;
    flex-direction: column;
    height: 100%;
    margin-left: 16px;
  }
}
.card-wrapper-main {
  height: calc(100% - 62px);
}
.tree-box {
  height: calc(100% - 32px);
  padding: 16px 0;
  box-sizing: border-box;
  overflow: auto;
}

.card-wrapper {
  background: #fff;
  border-radius: 4px;
}
.catalog-wrapper {
  height: calc(100% - 336px);
  margin-bottom: 16px;
}
.left-bottom-box {
  height: 320px;
  background: #fff;
  border-radius: 4px;
  overflow: hidden;
}
.bottom-box {
  height: 100%;
  overflow: hidden;
}
.bottom-box-active {
  height: 0;
}
.left-bottom-box-record {
  height: 206px;
}
.left-bottom-box-active {
  height: 48px;
}
.catalog-wrapper-record {
  height: calc(100% - 222px);
}
.catalog-wrapper-active {
  height: calc(100% - 60px);
}
.search-box {
  padding: 0 16px;
}

.custom-tree-node {
  display: flex;
  justify-content: space-between;
  align-items: center;
  width: 100%;
  .custom-tree-box {
    display: flex;
    align-items: center;
    justify-content: space-between;
    width: 100%;
    padding-right: 8px;
    box-sizing: border-box;
    img {
      margin-right: 6px;
      width: 18px;
      height: 18px;
      &:last-child {
        // margin-right: 0;
      }
    }
    .custom-tree-node-label-box {
      width: calc(100% - 24px);
      display: flex;
      align-items: center;
    }
    .custom-tree-node-label {
      max-width: 160px;
      overflow: hidden;
      text-overflow: ellipsis;
      white-space: nowrap;
    }
    .icon-ai {
      width: 16px;
      height: 16px;
    }
  }
}

/deep/ .el-tree-node__content {
  height: 40px;
}

/deep/ .el-tree-node__label {
  padding: 0;
  height: 40px;
  line-height: 40px;
  box-sizing: border-box;
}
.left-bottom-header {
  height: 48px;
  line-height: 48px;
  padding: 0 16px;
  box-sizing: border-box;
  border-bottom: 1px solid #ededed;
  display: flex;
  justify-content: space-between;
  align-items: center;
  cursor: pointer;
}
.left-bottom-header-title {
  color: #333;
  font-size: 16px;
}
.el-icon-arrow-right {
  font-size: 16px;
  color: #333;
  cursor: pointer;
  transition: transform 0.3s ease-in-out;
}
.el-icon-arrow-right.is-active {
  transform: rotate(90deg);
}
.wrapper-right-main {
  // height: calc(100% - 216px);
  height: 100%;
  position: relative;
}
.wrapper-right-box {
  height: calc(100% - 62px);
  position: relative;
}
.wrapper-right-bottom {
  height: 200px;
  margin-top: 16px;
}
.icon-close-all {
  position: absolute;
  right: 16px;
  top: 7px;
  color: #666;
  cursor: pointer;
}
.page-wrapper-dark {
  .card-wrapper {
    background: #05396c;
  }
  /deep/ .el-tabs__item {
    color: rgba(255, 255, 255, 0.8);
  }
  /deep/ .el-tabs__item:hover {
    color: #fff;
  }
  /deep/ .el-tabs__item.is-active {
    color: #fff;
  }
  /deep/ .el-input__inner {
    background: #05396c;
    border-color: #0073e5;
    color: #fff;
  }
  /deep/ .el-button {
    background: #0073e5;
    border-color: #0073e5;
    color: #fff;
  }
  /deep/ .el-tabs__header i {
    color: #fff;
  }
  /deep/ .el-input-group__append .el-button {
    margin: -11px;
  }
  .tree-box {
    background: #05396c;
    /deep/ .el-tree {
      background: #05396c;
      color: #fff;
    }
    /deep/
      .el-tree--highlight-current
      .el-tree-node:not(.is-disabled).is-current
      > .el-tree-node__content {
      background: #1e4c7a;
    }
  }
  .left-bottom-box {
    background: #05396c;
  }
  .left-bottom-header {
    color: #fff;
    border-color: #0073e5;
  }
  .presets-label {
    color: #fff;
  }
}
.not-install-box {
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  width: 100%;
  height: 100%;
}
.no-install-btn-box {
  display: flex;
  justify-content: center;
}
</style>