<template>
  <div class="dse-login-box">
    <img
      src="@/assets/images/login1/login-bg.png"
      alt=""
      srcset=""
      class="login-bg"
    />
    <div class="dse-login-main">
      <img
        src="@/assets/images/login1/logo.png"
        alt=""
        srcset=""
        class="dse-logo"
      />
      <div class="name-title">{{ sysTitle }}</div>
      <div class="login-form-box">
        <el-form
          :model="userInfo"
          class="login-form"
          :rules="rules"
          ref="ruleForm"
        >
          <el-form-item prop="user">
            <el-input
              v-model="userInfo.user"
              class="input-call"
              placeholder="请输入用户名"
            >
              <img
                :src="require('@/assets/images/login1/icon-login-user.png')"
                alt=""
                srcset=""
                slot="prefix"
                class="input-prefix"
              />
            </el-input>
          </el-form-item>
          <el-form-item prop="password">
            <el-input
              :type="inputType"
              v-model="userInfo.password"
              class="input-call"
              placeholder="请输入密码"
            >
              <img
                :src="require('@/assets/images/login1/icon-luck.png')"
                alt=""
                srcset=""
                slot="prefix"
                class="input-prefix"
              />
              <img
                :src="require('@/assets/images/login1/icon-no-view.png')"
                v-if="inputType === 'password'"
                alt=""
                srcset=""
                slot="suffix"
                class="input-suffix"
                @click="inputType = ''"
              />
              <img
                :src="require('@/assets/images/login1/icon-view.png')"
                v-else
                alt=""
                srcset=""
                slot="suffix"
                class="input-suffix"
                @click="inputType = 'password'"
              />
            </el-input>
          </el-form-item>
        </el-form>

        <el-button type="primary" class="btn" @click="btnLoginClick"
          >登录</el-button
        >
        <!-- <el-button type="primary" class="btn" @click="btnEncryptLoginClick"
            >加密登录</el-button
          > -->
      </div>
    </div>
    <div class="login-idente">
      <div class="idente-item">版权所有：东深智水</div>
      <div>技术支持：东深智水科技(深圳)股份有限公司</div>
    </div>
  </div>
</template>
<script>
import { loginMixin } from "@/mixins/loginMixin";
export default {
  name: "loginIndex1",
  data() {
    return {};
  },
  mixins: [loginMixin],
};
</script>
<style scoped>
.dse-login-box {
  width: 100%;
  height: 100%;
  position: relative;
}
.login-bg {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
}
.dse-login-main {
  position: absolute;
  top: 50%;
  transform: translate(0, -50%);
  width: 460px;
  right: 12.5vw;
  background: rgba(0, 0, 0, 0.1);
  border: 1px solid rgba(255, 255, 255, 0.4);
  border-radius: 8px;
  z-index: 10;
  backdrop-filter: blur(5px);
}
.dse-logo {
  width: 110px;
  height: 33px;
  margin-top: 32px;
  margin-left: 40px;
}
.name-title {
  font-family: "DseNameTitleFont";
  font-size: 44px;
  color: #fff;
  text-align: center;
  margin-top: 40px;
}
.login-form-box {
  width: 348px;
  margin: 48px auto 0;
}
.input-prefix {
  width: 20px;
  height: 20px;
  position: absolute;
  left: 6px;
  top: 50%;
  transform: translate(0%, -50%);
}
.input-suffix {
  width: 20px;
  height: 20px;
  cursor: pointer;
  position: absolute;
  right: 6px;
  top: 50%;
  transform: translate(0%, -50%);
}
.btn {
  width: 100%;
  font-size: 16px;
  margin-top: 64px;
  margin-bottom: 80px;
}

.login-idente {
  position: absolute;
  bottom: 24px;
  left: 0;
  width: 100%;
  display: flex;
  justify-content: center;
  color: #FFF;
}
.idente-item {
  margin-right: 2%;
}
</style>

<style>
.dse-login-box .el-button {
  height: 40px;
}
.dse-login-box .el-input__inner {
  height: 40px;
  line-height: 40px;
  background: rgba(255, 255, 255, 0.2);
  border: 1px solid rgba(255, 255, 255, 0.4);
  color: #FFF;
}
.dse-login-box .el-input__inner::placeholder {
  color: #FFF;
}
.dse-login-box .el-input__inner:-internal-autofill-selected {
  background-color: rgba(255, 255, 255, 0.2) !important;
  border: 1px solid rgba(255, 255, 255, 0.4);
  color: #FFF;
}
.dse-login-box .el-input--prefix .el-input__inner {
  padding-left: 36px;
}

</style>
