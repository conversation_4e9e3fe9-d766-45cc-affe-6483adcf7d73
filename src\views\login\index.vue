<template>
  <div class="login-box">
    <div class="login-bg-box">
      <img
        src="@/assets/images/login/login-bg.png"
        alt=""
        srcset=""
        class="login-bg"
      />
      <div class="login-idente">
        <div class="idente-item">版权所有：东深智水</div>
        <div>技术支持：东深智水科技(深圳)股份有限公司</div>
      </div>
    </div>
    <div class="login-main">
      <img
        src="@/assets/images/login/logo.png"
        alt=""
        srcset=""
        class="dse-logo"
      />

      <div class="login-main-box">
        <div class="name-title">{{sysTitle}}</div>
        <div class="login-form-box">
          <el-form
            :model="userInfo"
            :rules="rules"
            class="login-form"
            ref="ruleForm"
          >
            <el-form-item prop="user">
              <el-input
                v-model="userInfo.user"
                class="input-call"
                placeholder="请输入用户名"
              >
                <img
                  :src="require('@/assets/images/login/icon-login-user.png')"
                  alt=""
                  srcset=""
                  slot="prefix"
                  class="input-prefix"
                />
              </el-input>
            </el-form-item>
            <el-form-item prop="password">
              <el-input
                :type="inputType"
                v-model="userInfo.password"
                class="input-call"
                placeholder="请输入密码"
              >
                <img
                  :src="require('@/assets/images/login/icon-luck.png')"
                  alt=""
                  srcset=""
                  slot="prefix"
                  class="input-prefix"
                />
                <img
                  :src="require('@/assets/images/login/icon-no-view.png')"
                  v-if="inputType === 'password'"
                  alt=""
                  srcset=""
                  slot="suffix"
                  class="input-suffix"
                  @click="inputType = ''"
                />
                <img
                  :src="require('@/assets/images/login/icon-view.png')"
                  v-else
                  alt=""
                  srcset=""
                  slot="suffix"
                  class="input-suffix"
                  @click="inputType = 'password'"
                />
              </el-input>
            </el-form-item>
          </el-form>

          <el-button type="primary" class="btn" @click="btnLoginClick"
            >登录</el-button
          >
          <!-- <el-button type="primary" class="btn" @click="btnEncryptLoginClick"
            >加密登录</el-button
          > -->
        </div>
      </div>
    </div>
  </div>
</template>
<script>

import { loginMixin } from "@/mixins/loginMixin"
export default {
  name: "loginIndex",
  data() {
    return {
    };
  },
  mixins: [loginMixin],
  methods: {
  },
};
</script>
<style scoped>
.login-box {
  display: flex;
  height: 100%;
}
.login-bg-box {
  flex: 1220;
  height: 100%;
  position: relative;
}
.login-bg {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
}
.login-idente {
  position: absolute;
  bottom: 24px;
  left: 0;
  width: 100%;
  display: flex;
  justify-content: center;
  color: #666666;
}
.idente-item {
  margin-right: 6%;
}
.login-main {
  flex: 700;
  height: 100%;
  background: #e1ecfa;
  position: relative;
}
.dse-logo {
  width: 10.625vw;
  height: auto;
  margin-top: 1.67vw;
  margin-left: 2.08vw;
}
.name-title {
  font-family: "DseNameTitleFont";
  color: #333;
  font-size: 2.92vw;
  text-align: center;
}
.login-main-box {
  position: absolute;
  top: 50%;
  left: 50%;
  transform: translate(-50%, -50%);
  width: 100%;
}
.login-form-box {
  width: 22.92vw;
  margin: 3.64vw auto 0;
}

.input-prefix {
  width: 1.04vw;
  height: 1.04vw;
  position: absolute;
  left: 0.83vw;
  top: 50%;
  transform: translate(0%, -50%);
}
.input-suffix {
  width: 1.04vw;
  height: 1.04vw;
  cursor: pointer;
  position: absolute;
  right: 0.83vw;
  top: 50%;
  transform: translate(0%, -50%);
}
.btn {
  width: 100%;
  font-size: 16px;
  margin-top: 3.6vw;
}
</style>
<style>
.login-form-box .el-button {
  height: 2.92vw;
  border-radius: 8px;
}
.login-form-box .el-input__inner {
  height: 2.92vw;
  line-height: 2.92vw;
  border-radius: 8px;
}
.login-form-box .el-input--prefix .el-input__inner {
  padding-left: 2.5vw;
}
.login-form-box .el-form-item {
  margin-bottom: 1.25vw;
}
</style>
