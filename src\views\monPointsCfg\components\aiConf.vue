<template>
  <el-dialog
    :title="title"
    :visible.sync="showDialog"
    :close-on-click-modal="false"
    destroy-on-close
    :before-close="closeHandle"
    width="640px"
  >
    <el-form
      ref="formData"
      :label-position="'top'"
      label-width="140px"
      :rules="rules"
      :model="formData"
    >
      <el-row>
        <el-col :span="24">
          <el-form-item label="AI算法" prop="algorithmCode">
            <el-select
              style="width: 100%"
              v-model="formData.algorithmCode"
              placeholder="请选择"
              @change="algorithmChange"
            >
              <el-option
                v-for="item in algorithmOptions"
                :key="item.code"
                :label="item.name"
                :value="item.code"
              >
              </el-option>
            </el-select>
          </el-form-item>
        </el-col>
      </el-row>
      <el-row v-if="formData.algorithmType === 'alarm'">
        <el-col :span="11">
          <el-form-item label="预警等级" prop="alarmType">
            <pt-dict-down-list
              :selectItem="formData.alarmType"
              :type="dictCodes.alarmType"
              :isDefault="false"
              @change="typeChangeHandle"
            ></pt-dict-down-list>
          </el-form-item>
        </el-col>
        <el-col :span="11" :offset="2">
          <el-form-item label="阈值" prop="alarmThreshold">
            <el-input-number
              v-model="formData.alarmThreshold"
              :min="0"
              :max="0.99"
              :step="0.1"
              step-strictly
              :precision="2"
              style="width: 100%"
            ></el-input-number>
          </el-form-item>
        </el-col>
      </el-row>
      <el-row>
        <el-col :span="11">
          <el-form-item label="AI实时" prop="realtimeEnable">
            <el-switch v-model="formData.realtimeEnable"></el-switch>
          </el-form-item>
        </el-col>
        <el-col :span="11" :offset="2">
          <el-form-item label="AI监测" prop="alarmEnable">
            <el-switch v-model="formData.alarmEnable"></el-switch>
          </el-form-item>
        </el-col>
      </el-row>
      <el-row v-if="formData.algorithmType === 'alarm'">
        <el-col :span="11">
          <el-form-item label="检测开始时间" prop="alarmStartTime">
            <el-time-picker
              v-model="formData.alarmStartTime"
              placeholder="开始时间"
              style="width: 100%"
              :clearable="false"
              :picker-options="startPickerOptions"
              value-format="HH:mm:ss"
            >
            </el-time-picker>
          </el-form-item>
        </el-col>
        <el-col :span="11" :offset="2">
          <el-form-item label="检测结束时间" prop="alarmEndTime">
            <el-time-picker
              v-model="formData.alarmEndTime"
              placeholder="结束时间"
              style="width: 100%"
              :clearable="false"
              :picker-options="endPickerOptions"
              value-format="HH:mm:ss"
            >
            </el-time-picker>
          </el-form-item>
        </el-col>
      </el-row>
      <el-row v-else>
        <el-col :span="11">
          <el-form-item label="检测数据同步频次" prop="dataSyncFreq">
            <el-input v-model="formData.dataSyncFreq">
              <el-select
                v-model="formData.dataSyncFreqType"
                style="width: 80px"
                slot="append"
                placeholder="请选择"
                @change="syncFreqChange"
              >
                <el-option label="分" value="1" :key="1"></el-option>
                <el-option label="秒" value="2" :key="2"></el-option>
              </el-select>
            </el-input>
          </el-form-item>
        </el-col>
      </el-row>
      <el-row>
        <el-col :span="24">
          <el-form-item label="备注" prop="remark">
            <el-input v-model="formData.remark"></el-input>
          </el-form-item>
        </el-col>
      </el-row>
    </el-form>
    <span slot="footer" class="dialog-footer">
      <el-button @click="closeHandle">取消</el-button>
      <el-button type="primary" @click="saveHandle" :loading="loading">确定</el-button>
    </span>
  </el-dialog>
</template>

<script>
import dayjs from "dayjs";
import {
  algorithmListRequest,
  saveOrUpdateAlgorithmRequest,
  getAlgorithmCofRequest,
} from "@/api/ai/aiData";
import PtDictDownList from "@/components/PtDictDownList/index.vue";
export default {
  name: "aiConf",
  components: {
    PtDictDownList,
  },
  props: {
    id: {
      type: String,
      default: "",
    },
  },
  computed: {
    startPickerOptions() {
      if (this.formData.alarmEndTime) {
        return {
          selectableRange: "00:00:00 - " + this.formData.alarmEndTime,
        };
      } else {
        return {
          selectableRange: "00:00:00 - 23:59:59",
        };
      }
    },
    endPickerOptions() {
      if (this.formData.alarmStartTime) {
        return {
          selectableRange: this.formData.alarmStartTime + " - 23:59:59",
        };
      } else {
        return {
          selectableRange: "00:00:00 - 23:59:59",
        };
      }
    },
  },
  data() {
    return {
      showDialog: true,
      title: "AI算法配置",
      formData: {
        id: "",
        siteId: "",
        algorithmName: "",
        algorithmCode: "",
        algorithmType: "",
        alarmThreshold: "",
        alarmType: "",
        realtimeEnable: true,
        alarmEnable: true,
        alarmStartTime: "",
        alarmEndTime: "",
        dataSyncFreq: "",
        dataSyncFreqType: "1",
        remark: "",
      },
      algorithmOptions: [],
      rules: {
        algorithmCode: [
          { required: true, message: "请选择AI算法", trigger: "blur" },
        ],
        alarmType: [
          { required: true, message: "请选择预警等级", trigger: "blur" },
        ],
        alarmThreshold: [
          { required: true, message: "请输入阈值", trigger: "blur" },
        ],
        realtimeEnable: [
          { required: true, message: "请选择AI实时", trigger: "blur" },
        ],
        alarmEnable: [
          { required: true, message: "请选择AI监测", trigger: "blur" },
        ],
        dataSyncFreq: [
          {
            required: true,
            message: "请输入检测数据同步频次",
            trigger: "blur",
          },
          { pattern: /^\d+(\.\d+)?$/, message: "请输入正数", trigger: "blur" },
        ],
      },
      currentData: "",
      loading: false
    };
  },
  created() {
    this.getAlgorithData();
  },
  mounted() {
    this.currentData = dayjs().format("YYYY-MM-DD");
    this.showDialog = true;
    this.formData.siteId = this.id
    this.getAlgorithDetailData();
  },
  methods: {
    getAlgorithData() {
      algorithmListRequest().then((res) => {
        if (res.code === 200) {
          this.algorithmOptions = res.data;
        }
      });
    },
    algorithmChange(value) {
      if (value) {
        let alg = this.algorithmOptions.find((d) => d.code === value);
        this.formData.algorithmType = (alg && alg.type) || "";
        this.formData.algorithmName = (alg && alg.name) || "";
      }
    },
    getAlgorithDetailData() {
      getAlgorithmCofRequest(this.id).then((res) => {
        if (res.code === 200 && res.data) {
          this.formData = res.data;
          if(this.formData.dataSyncFreq >= 60) {
            this.formData.dataSyncFreqType = '1'
            this.formData.dataSyncFreq = this.formData.dataSyncFreq / 60
          } else {
            this.formData.dataSyncFreqType = '2'
          }
        }
      });
    },
    typeChangeHandle(value) {
      this.formData.alarmType = value;
    },
    syncFreqChange(value) {
      this.formData.dataSyncFreqType = value;
      this.$forceUpdate()
    },
    closeHandle() {
      this.$emit("close");
    },
    saveHandle() {
      this.$refs["formData"].validate((valid) => {
        if (valid) {
          this.loading = true
          let dataSyncFreq = this.formData.dataSyncFreq
          let alarmType = this.formData.algorithmType !== 'alarm' ? null : this.formData.alarmType
          if(this.formData.dataSyncFreqType === '1') {
            dataSyncFreq = this.formData.dataSyncFreq * 60
          }
          saveOrUpdateAlgorithmRequest({
            ...this.formData,
            dataSyncFreq,
            alarmType
          }).then((res) => {
            this.loading = false
            if (res.code === 200) {
              this.$message({
                type: "success",
                message: "保存成功",
              });
              this.$emit('success')
            }
          }).catch(() => {
            this.loading = false
          });
        }
      });
    },
  },
};
</script>

<style lang="scss" scoped>
</style>