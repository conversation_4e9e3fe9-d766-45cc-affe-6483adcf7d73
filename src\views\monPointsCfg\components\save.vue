<template>
  <el-form
    ref="formData"
    :label-position="'top'"
    label-width="140px"
    :model="formData"
  >
    <div v-show="step === 1">
      <el-row>
        <el-col :span="24">
          <el-form-item
            label="服务器名称"
            prop="serverId"
            :rules="rules.serverId"
          >
            <div style="display: flex; align-items: center">
              <el-select
                style="width: 100%"
                v-model="formData.serverId"
                placeholder="请选择"
                @change="changeServerHandle"
              >
                <el-option
                  v-for="item in videoServerOptions"
                  :key="item.id"
                  :label="item.name"
                  :value="item.id"
                >
                </el-option>
              </el-select>
              <el-button type="primary" style="margin-left: 10px" @click="refreshSite"
                >刷新监测点</el-button
              >
            </div>
          </el-form-item>
        </el-col>
      </el-row>
      <el-row>
        <el-col :span="24">
          <el-form-item label="" prop="list" :rules="rules.list">
            <el-transfer
              filterable
              :filter-method="filterMethod"
              filter-placeholder="请输入关键字"
              :titles="['待选视频点', '已选视频点']"
              :props="{
                key: 'cameraIndexCode',
                label: 'name',
              }"
              v-model="formData.list"
              :data="siteOptions"
            >
            </el-transfer>
          </el-form-item>
        </el-col>
      </el-row>
    </div>
    <div v-show="step === 2" class="list-wrapper">
      <el-collapse v-model="activeNames">
        <template v-for="(item, index) in formData.selectedSiteList">
          <el-collapse-item :key="index" :name="item.cameraIndexCode">
            <template slot="title">
              <div class="title">
                <span>{{ item.name }}</span>
                <span>编号：{{ item.cameraIndexCode }}</span>
              </div>
            </template>
            <el-row>
              <el-col :span="11">
                <el-form-item label="视频监控点类型">
                  <pt-dict-down-list
                    :selectItem="item.cameraType"
                    :type="dictCodes.cameraType"
                    :isDefault="false"
                    @change="($event) => typeOptionsChange($event, item)"
                  ></pt-dict-down-list>
                </el-form-item>
              </el-col>
              <el-col :span="11" :offset="2">
                <el-form-item label="录像存储位置">
                  <el-select
                    style="width: 100%"
                    v-model="item.recordLocation"
                    placeholder="请选择"
                  >
                    <el-option
                      v-for="item in recordLocationOptions"
                      :key="item.value"
                      :label="item.label"
                      :value="item.value"
                    >
                    </el-option>
                  </el-select>
                </el-form-item>
              </el-col>
            </el-row>
            <el-row>
              <el-col :span="11">
                <el-form-item label="拉流方式">
                  <el-select
                    style="width: 100%"
                    v-model="item.transType"
                    placeholder="请选择"
                  >
                    <el-option
                      v-for="item in protocolData[item.jrtype]"
                      :key="item.key"
                      :label="item.value"
                      :value="item.key"
                    >
                    </el-option>
                  </el-select>
                </el-form-item>
              </el-col>
              <el-col :span="11" :offset="2">
                <el-form-item label="行政区划">
                  <el-tree-select
                    v-model="item.adcd"
                    :default-expanded-keys="defaultExpandedKeys"
                    :data="treeData"
                    node-key="code"
                    :props="treeProps"
                    clearable
                  >
                    <template v-slot:default="scope">
                      <span class="node-label">{{ scope.node.label }}</span>
                    </template>
                  </el-tree-select>
                </el-form-item>
              </el-col>
            </el-row>
            <el-row>
              <el-col :span="11">
                <el-form-item
                  label="管理单位"
                  :prop="'selectedSiteList[' + index + '].orgId'"
                  :rules="rules.lgtd"
                >
                  <dse-tree-select
                    ref="menuTreeRef"
                    listKey="organizationTree"
                    :url="navParams.url"
                    :autoParam="navParams.autoParam"
                    :otherParam="navParams.otherParam"
                    :tree-props="menuTreeOptions"
                    :treeValue="item.orgId"
                    :defaultValue="item.orgName"
                    :expandedKeys="expandedKeys"
                    @change="($event) => selectChanges($event, item)"
                  ></dse-tree-select>
                </el-form-item>
              </el-col>
              <el-col :span="11" :offset="2">
                <el-form-item label="监测点地址">
                  <el-input v-model="item.address"></el-input>
                </el-form-item>
              </el-col>
            </el-row>
            <el-row>
              <el-col :span="11">
                <el-form-item
                  label="经度"
                  :prop="'selectedSiteList[' + index + '].lgtd'"
                  :rules="rules.lgtd"
                >
                  <el-input v-model="item.lgtd"></el-input>
                </el-form-item>
              </el-col>
              <el-col :span="11" :offset="2">
                <el-form-item
                  label="纬度"
                  :prop="'selectedSiteList[' + index + '].lttd'"
                  :rules="rules.lttd"
                >
                  <el-input v-model="item.lttd"></el-input>
                </el-form-item>
              </el-col>
            </el-row>
            <el-row>
              <el-col :span="11">
                <el-form-item label="排序">
                  <el-input-number
                    v-model="item.sortNo"
                    :min="0"
                    :precision="0"
                    :step="1"
                    :max="9999"
                    label="顺序"
                    style="width: 100%"
                  ></el-input-number>
                </el-form-item>
              </el-col>
            </el-row>
            <el-row>
              <el-col :span="24">
                <el-form-item label="备注" prop="remark">
                  <el-input
                    v-model="item.remark"
                    type="textarea"
                    maxlength="500"
                    show-word-limit
                    placeholder="请输入备注"
                    :autosize="{ minRows: 3, maxRows: 6 }"
                  ></el-input>
                </el-form-item>
              </el-col>
            </el-row>
          </el-collapse-item>
        </template>
      </el-collapse>
    </div>
  </el-form>
</template>

<script>
import { listSiteByServerIdRequest } from "@/api/play/playData";
import {
  QueryWholeDivisionTree4S,
  QUERY_ORGANIZATION_TREE_4S_URL,
} from "@/api/baseInfo/baseInfoData";
import { SaveSiteRequest } from "@/api/site/siteData";
import { QueryServerListRequest, RefreshSiteRequest } from "@/api/server/serverData";
import { availableProtocolRequest } from "@/api/common/commonData";

import PtDictDownList from "@/components/PtDictDownList/index.vue";
import DseTreeSelect from "@/components/DseTreeSelect";
export default {
  name: "saveComponents",
  components: {
    PtDictDownList,
    DseTreeSelect,
  },
  data() {
    return {
      formData: {
        serverId: "",
        list: [],
        selectedSiteList: [],
      },
      step: 1,
      rules: {
        serverId: [
          { required: true, message: "请选择服务器名称", trigger: "blur" },
        ],
        list: [{ required: true, message: "请选择视频点", trigger: "blur" }],
        lgtd: [
          {
            pattern:
              /^-?(180(\.0{1,6})?|1[0-7][0-9](\.\d{1,6})?|[1-9][0-9](\.\d{1,6})?|[1-9](\.\d{1,6})?)$/,
            message: "经度格式不正确",
            trigger: "blur",
          },
        ],
        lttd: [
          {
            pattern:
              /^-?(90(\.0{1,6})?|[1-8][0-9](\.\d{1,6})?|[1-9](\.\d{1,6})?)$/,
            message: "纬度格式不正确",
            trigger: "blur",
          },
        ],
      },
      videoServerOptions: [],
      siteOptions: [],
      activeNames: [],
      filterMethod(query, item) {
        return item.name && item.name.indexOf(query) > -1;
      },
      treeData: [], 
      treeProps: {
        label: "name",
        children: "childsNode",
      },
      recordLocationOptions: [
        {
          label: "中心存储",
          value: "0",
        },
        {
          label: "设备存储",
          value: "1",
        },
      ], //录像存储位置0-中心存储，1-设备存储
      transTypeOptions: [
        {
          label: "UDP",
          value: "0",
        },
        {
          label: "TCP",
          value: "1",
        },
      ], //传输协议0-UDP，1-TCP
      transOptions: [], //传输协议
      protocolData: null,
      navParams: {
        url: QUERY_ORGANIZATION_TREE_4S_URL,
        autoParam: ["id=pid"],
        otherParam: { isSync: true },
      },
      menuTreeOptions: {
        label: "name",
        children: "childsNode",
      },
      expandedKeys: [],
    };
  },
  mounted() {
    this.getServerList();
    this.getDivisionData();
    this.getTransData();
  },
  methods: {
    getServerList() {
      QueryServerListRequest("").then((res) => {
        if (res.code === 200) {
          this.videoServerOptions = res.data;
        }
      });
    },
    getDivisionData() {
      let params = {
        isSync: false,
        pid: "",
      };
      QueryWholeDivisionTree4S(params).then((res) => {
        if (res.code === 200) {
          this.treeData = res.data.divisionTree;
          this.defaultExpandedKeys = this.treeData.map((d) => d.code);
        }
      });
    },
    getTransData() {
      availableProtocolRequest().then((res) => {
        if (res.code === 200) {
          this.protocolData = res.data;
        }
      });
    },
    changeServerHandle(value) {
      if (value) {
        listSiteByServerIdRequest({ serverId: value })
          .then((res) => {
            if (res.code === 200) {
              this.siteOptions = res.data;
            } else {
              this.siteOptions = [];
            }
          })
          .catch(() => {
            this.siteOptions = [];
          });
      } else {
        this.siteOptions = [];
      }
    },
    typeOptionsChange(value, item) {
      item.cameraType = value || "";
    },
    selectChanges(node, item) {
      item.orgId = (node && node.id) || "";
      item.orgName = (node && node.name) || "";
    },
    transOptionsChange(value, item) {
      item.transType = value || "";
    },
    changeStep(val) {
      if (val === 2) {
        this.$refs["formData"].validate((valid) => {
          if (valid) {
            let arr = [];
            this.siteOptions.forEach((data) => {
              if (this.formData.list.includes(data.cameraIndexCode)) {
                arr.push(data);
              }
            });
            this.formData.selectedSiteList = arr;
            this.activeNames = this.formData.list;
            this.step = val;
            this.$emit("changeStep", val);
          } else {
            return false;
          }
        });
      } else {
        this.step = val;
        this.$emit("changeStep", val);
      }
    },
    saveFormData() {
      const loading = this.$loading({
        lock: true,
        text: "加载中",
        spinner: "el-icon-loading",
        background: "rgba(0, 0, 0, 0.7)",
      });
      let params = {
        serverId: this.formData.serverId,
        list: this.formData.selectedSiteList,
      };
      SaveSiteRequest(params)
        .then((res) => {
          loading.close();
          if (res.code === 200) {
            this.$message({
              type: "success",
              message: "保存成功",
            });
            this.$emit("opFin");
          }
        })
        .catch((err) => {
          loading.close();
        });
    },
    refreshSite() {
      if (!this.formData.serverId) {
        this.$message({ type: "warning", message: "请选择服务器" });
        return;
      }
      RefreshSiteRequest(this.formData.serverId).then((res) => {
        if (res.code === 200) {
          this.$message({ type: "success", message: "刷新成功" });
          this.changeServerHandle(this.formData.serverId);
        }
      });
    },
  },
};
</script>

<style lang="scss" scoped>
.list-wrapper {
  height: 500px;
  overflow-y: auto;
}
.title {
  display: flex;
  justify-content: space-between;
  width: 100%;
  padding-left: 20px;
}
/deep/ .el-transfer-panel {
  width: 248px;
}

/deep/ .el-transfer-panel__filter {
  text-align: center;
  margin: 15px;
  -webkit-box-sizing: border-box;
  box-sizing: border-box;
  display: block;
  width: auto;
}
/deep/ .el-transfer__buttons {
  .el-button {
    display: block;
    margin: 0;
    &:first-child {
      margin-bottom: 10px;
    }
  }
}
/deep/ .el-collapse-item__header {
  background: #f7f7f7;
  border: 1px solid #d9d9d9;
  padding: 0 12px;
  position: relative;
  .el-collapse-item__arrow {
    position: absolute;
    left: 10px;
  }
}
/deep/ .el-collapse-item__wrap {
  border-left: 1px solid #d9d9d9;
  border-right: 1px solid #d9d9d9;
  border-bottom: 0;
}
/deep/ .el-collapse {
  .el-collapse-item:last-child {
    border-bottom: 1px solid #d9d9d9;
  }
}
/deep/ .el-collapse-item__content {
  padding: 16px;
}
/deep/ .el-transfer-panel__item.el-checkbox .el-checkbox__label {
  overflow: inherit;
}
</style>