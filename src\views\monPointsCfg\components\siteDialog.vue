<template>
  <el-dialog
    :title="title"
    :visible.sync="showDialog"
    :close-on-click-modal="false"
    destroy-on-close
    :before-close="closeHandle"
    width="640px"
  >
    <save
      ref="save"
      v-if="operaType === 'add'"
      @changeStep="(val) => (infoStep = val)"
      @opFin="opFin"
    ></save>
    <update ref="update" v-else @opFin="opFin" :itemData="itemData"></update>
    <span slot="footer" class="dialog-footer" v-if="operaType === 'add'">
      <el-button @click="closeHandle">取消</el-button>
      <el-button v-if="infoStep === 2" @click="opStep(-1)">上一步</el-button>
      <el-button v-if="infoStep === 1" type="primary" @click="opStep(1)"
        >下一步</el-button
      >
      <el-button v-if="infoStep === 2" type="primary" @click="opSaveConfirm"
        >确定</el-button
      >
    </span>
    <span slot="footer" class="dialog-footer" v-else>
      <el-button @click="closeHandle">取消</el-button>
      <el-button type="primary" @click="opUpdateConfirm">确定</el-button>
    </span>
  </el-dialog>
</template>

<script>
import save from "./save.vue";
import update from "./update.vue";
export default {
  name: "siteDialog",
  props: {
    itemData: {
      type: Object,
      default() {
        return {}
      }
    },
    operaType: {
      type: String,
      default: 'add'
    }
  },  
  components: {
    save,
    update
  },
  data() {
    return {
      showDialog: true,
      infoStep: 1,
      title: "新增监控点",
      
    };
  },
  mounted() {
    this.showDialog = true
    if(this.operaType === 'add') {
      this.title = '新增监控点'
    } else {
      this.title = '编辑监控点'
    }
  },
  methods: {
    opUpdateConfirm() {
      this.$refs.update.saveFormData();
    },
    opSaveConfirm() {
      this.$refs.save.saveFormData();
    },
    opStep(value) {
      let step = this.infoStep + value
      if(step === 1) {
        this.title = '新增监测点'
      } else  {
        this.title = '配置监控点基础信息'
      }
      this.$refs.save.changeStep(step);
    },
    opFin() {
      this.$emit('success')
    },
    closeHandle() {
      this.$emit('close')
    }
  },
};
</script>

<style>
</style>