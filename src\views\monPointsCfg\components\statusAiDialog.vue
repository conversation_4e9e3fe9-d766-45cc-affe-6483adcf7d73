<template>
  <el-dialog
    :title="title"
    :visible.sync="showDialog"
    :close-on-click-modal="false"
    destroy-on-close
    :before-close="closeHandle"
    width="640px"
  >
    <el-form
      ref="formData"
      label-position="left"
      label-width="140px"
      :inline="true"
      :rules="rules"
      :model="formData"
    >
      <title-cell
        v-for="item in tabsList"
        :key="item.id"
        :title="item.algorithmName"
        type="1"
      >
        <div class="form-item-cell">
          <el-form-item label="" prop="realtimeEnable">
            <div class="form-label">
              <span class="form-red">*</span>
              <span class="form-label-text">AI实时监测</span>
              <el-tooltip
                class="item"
                effect="dark"
                content="配置后实时在视频界面显示检测结果"
                placement="top"
              >
                <img
                  src="@/assets/images/icon/icon-feedback.png"
                  class="icon-feedback"
                  alt=""
                  srcset=""
                />
              </el-tooltip>
            </div>
            <el-switch v-model="item.realtimeEnable"></el-switch>
          </el-form-item>

          <el-form-item label="" prop="alarmEnable">
            <div class="form-label">
              <span class="form-red">*</span>
              <span class="form-label-text">AI后台检测</span>
              <el-tooltip
                class="item"
                effect="dark"
                content="配置后在后台监测并按照频率生成信息"
                placement="top"
              >
                <img
                  src="@/assets/images/icon/icon-feedback.png"
                  class="icon-feedback"
                  alt=""
                  srcset=""
                />
              </el-tooltip>
            </div>
            <el-switch v-model="item.alarmEnable"></el-switch>
          </el-form-item>
        </div>
      </title-cell>
    </el-form>
    <span slot="footer" class="dialog-footer">
      <el-button @click="closeHandle">取消</el-button>
      <el-button type="primary" @click="saveHandle" :loading="loading"
        >确定</el-button
      >
    </span>
  </el-dialog>
</template>
<script>
import {
  algorithmListRequest,
  getAlgorithmCofRequest,
  saveOrUpdateAlgorithmRequest,
} from "@/api/ai/aiData";

import titleCell from "@/components/titleCell/index.vue";
export default {
  components: {
    titleCell,
  },
  props: {
    id: {
      type: String,
      default: "",
    },
  },
  data() {
    return {
      title: "算法状态",
      showDialog: true,
      formData: {},
      rules: {},
      algorithmOptions: [],
      tabsList: [],
      loading: false,
    };
  },
  mounted() {
    this.showDialog = true;
    if (this.id) {
      this.getDetailData();
    }
  },
  methods: {
    closeHandle() {
      this.$emit("close");
    },
    getDetailData() {
      getAlgorithmCofRequest(this.id).then((res) => {
        if (res.code === 200 && res.data) {
          let data = res.data;
          this.tabsList = data.map((d) => {
            return {
              ...d,
              name: d.algorithmName,
              code: d.algorithmCode,
            };
          });
        }
      });
    },
    getAlgorithData() {
      algorithmListRequest().then((res) => {
        if (res.code === 200) {
          let data = res.data;
          this.algorithmOptions = data;
        }
      });
    },
    saveHandle() {
      this.loading = true
      saveOrUpdateAlgorithmRequest(this.tabsList)
        .then((res) => {
          this.loading = false;
          if (res.code === 200) {
            this.$message({
              type: "success",
              message: "保存成功",
            });
            this.$emit('success');
          }
        })
        .catch(() => {
          this.loading = false;
        });
    },
  },
};
</script>
<style lang="scss" scoped>
.form-item-cell {
  display: flex;
  justify-content: space-between;
  margin-top: 16px;
}
.form-item-cell {
  /deep/ .el-form-item {
    flex: 1;
  }
}
.form-label {
  display: inline-block;
  margin-right: 10px;
}
.form-label-text {
  margin-right: 5px;
}
.icon-feedback {
  width: 12px;
  height: 12px;
}
.form-red {
  color: #f56c6c;
}
</style>