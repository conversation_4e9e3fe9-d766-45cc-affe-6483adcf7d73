<template>
  <el-form
    ref="formData"
    :label-position="'top'"
    label-width="140px"
    :rules="rules"
    :model="formData"
  >
    <el-row>
      <el-col :span="11">
        <el-form-item label="监控点名称" prop="name">
          <el-input v-model="formData.name" maxlength="50"></el-input>
        </el-form-item>
      </el-col>
      <el-col :span="11" :offset="2">
        <el-form-item label="关联服务器" prop="videoServerId">
          <el-select
            style="width: 100%"
            v-model="formData.videoServerId"
            placeholder="请选择"
            disabled
          >
            <el-option
              v-for="item in videoServerOptions"
              :key="item.id"
              :label="item.name"
              :value="item.id"
            >
            </el-option>
          </el-select>
        </el-form-item>
      </el-col>
    </el-row>
    <el-row>
      <el-col :span="11">
        <el-form-item label="视频监测点编号" prop="cameraIndexCode">
          <el-input
            disabled
            v-model="formData.cameraIndexCode"
            maxlength="32"
          ></el-input>
        </el-form-item>
      </el-col>
      <el-col :span="11" :offset="2">
        <el-form-item label="视频监控点类型" prop="cameraType">
          <pt-dict-down-list
            :selectItem="formData.cameraType"
            :type="dictCodes.cameraType"
            :isDefault="false"
            @change="typeChangeHandle"
          ></pt-dict-down-list>
        </el-form-item>
      </el-col>
    </el-row>
    <el-row>
      <el-col :span="11">
        <el-form-item label="录像存储位置" prop="recordLocation">
          <el-select
            style="width: 100%"
            v-model="formData.recordLocation"
            placeholder="请选择"
          >
            <el-option
              v-for="item in recordLocationOptions"
              :key="item.value"
              :label="item.label"
              :value="item.value"
            >
            </el-option>
          </el-select>
        </el-form-item>
      </el-col>
      <el-col :span="11" :offset="2">
        <el-form-item label="拉流方式" prop="transType">
          <el-select
            style="width: 100%"
            v-model="formData.transType"
            placeholder="请选择"
          >
            <el-option
              v-for="item in protocolOptions"
              :key="item.key"
              :label="item.value"
              :value="item.key"
            >
            </el-option>
          </el-select>
          <!-- <pt-dict-down-list
            :selectItem="formData.transType"
            :type="dictCodes.protocolType"
            :isDefault="false"
            @change="($event) => transOptionsChange($event)"
          ></pt-dict-down-list> -->
        </el-form-item>
      </el-col>
    </el-row>
    <el-row>
      <el-col :span="11">
        <el-form-item label="行政区划" prop="adcdArr">
          <el-tree-select
            v-model="formData.adcd"
            :defaultValue="formData.adcdName"
            :default-expanded-keys="defaultExpandedKeys"
            :data="treeData"
            node-key="code"
            :props="treeProps"
            clearable
            @change="changeAdHandle"
          >
            <template v-slot:default="scope">
              <span class="node-label">{{ scope.node.label }}</span>
            </template>
          </el-tree-select>
        </el-form-item>
      </el-col>
      <el-col :span="11" :offset="2">
        <el-form-item label="监测点地址" prop="address">
          <el-input v-model="formData.address" maxlength="100"></el-input>
        </el-form-item>
      </el-col>
    </el-row>
    <el-row>
      <el-col :span="11">
        <el-form-item
          label="管理单位"
          prop="orgId"
        >
          <dse-tree-select
            ref="menuTreeRef"
            listKey="organizationTree"
            :url="navParams.url"
            :autoParam="navParams.autoParam"
            :otherParam="navParams.otherParam"
            :tree-props="menuTreeOptions"
            :treeValue="formData.orgId"
            :defaultValue="formData.orgName"
            :expandedKeys="expandedKeys"
            @change="selectChanges"
          ></dse-tree-select>
        </el-form-item>
      </el-col>
      <el-col :span="11" :offset="2"> 
        <el-form-item label="排序" prop="sortNo">
          <el-input-number
            style="width: 100%"
            v-model="formData.sortNo"
            maxlength="6"
          ></el-input-number>
        </el-form-item>
      </el-col>
    </el-row>
    <el-row>
      <el-col :span="11">
        <el-form-item label="经度" prop="lgtd">
          <el-input v-model="formData.lgtd" maxlength="50"></el-input>
        </el-form-item>
      </el-col>
      <el-col :span="11" :offset="2">
        <el-form-item label="纬度" prop="lttd">
          <el-input v-model="formData.lttd" maxlength="50"></el-input>
        </el-form-item>
      </el-col>
    </el-row>
    <el-row>
      <el-col :span="24">
        <el-form-item label="备注" prop="remark">
          <el-input
            v-model="formData.remark"
            type="textarea"
            :autosize="{ minRows: 3, maxRows: 6 }"
            maxlength="500"
            show-word-limit
            placeholder="请输入备注"
          ></el-input>
        </el-form-item>
      </el-col>
    </el-row>
  </el-form>
</template>

<script>
import PtDictDownList from "@/components/PtDictDownList/index.vue";
import { QueryWholeDivisionTree4S, QUERY_ORGANIZATION_TREE_4S_URL } from "@/api/baseInfo/baseInfoData";
import { listServerAndSiteRequest } from "@/api/play/playData";
import { UpdateSiteRequest } from "@/api/site/siteData";
import { availableProtocolRequest } from "@/api/common/commonData";

import DseTreeSelect from "@/components/DseTreeSelect";

export default {
  name: "updateCom",
  components: {
    PtDictDownList,
    DseTreeSelect
  },
  props: {
    itemData: {
      type: Object,
      default() {
        return {};
      },
    },
  },
  watch: {
    itemData: {
      handler() {
        this.formData = JSON.parse(JSON.stringify(this.itemData));
      },
      immediate: true,
      deep: true,
    },
  },
  data() {
    return {
      formData: {},
      rules: {
        videoServerId: [
          { required: true, message: "请选择服务器名称", trigger: "blur" },
        ],
        name: [
          { required: true, message: "请输入监控点名称", trigger: "blur" },
        ],
        cameraIndexCode: [
          { required: true, message: "请输入视频监测点编号", trigger: "blur" },
        ],
        lgtd: [
          {
            pattern:
              /^-?(180(\.0{1,6})?|1[0-7][0-9](\.\d{1,6})?|[1-9][0-9](\.\d{1,6})?|[1-9](\.\d{1,6})?)$/,
            message: "经度格式不正确",
            trigger: "blur",
          },
        ],
        lttd: [
          {
            pattern:
              /^-?(90(\.0{1,6})?|[1-8][0-9](\.\d{1,6})?|[1-9](\.\d{1,6})?)$/,
            message: "经度格式不正确",
            trigger: "blur",
          },
        ],
      },
      treeData: [],
      defaultExpandedKeys: [],
      treeProps: {
        label: "name",
        children: "childsNode",
      },
      recordLocationOptions: [
        {
          label: "中心存储",
          value: "0",
        },
        {
          label: "设备存储",
          value: "1",
        },
      ], //录像存储位置0-中心存储，1-设备存储
      transTypeOptions: [
        {
          label: "UDP",
          value: "0",
        },
        {
          label: "TCP",
          value: "1",
        },
      ], //拉流方式0-UDP，1-TCP
      videoServerOptions: [],
      protocolOptions: [], //拉流方式
      navParams: {
        url: QUERY_ORGANIZATION_TREE_4S_URL,
        autoParam: ["id=pid"],
        otherParam: { isSync: true },
      },
      menuTreeOptions: {
        label: "name",
        children: "childsNode",
      },
      expandedKeys: []
    };
  },
  mounted() {
    this.getTransData();
    this.getDivisionData();
    this.getServerList();
  },
  methods: {
    getTransData() {
      availableProtocolRequest().then((res) => {
        if (res.code === 200) {
          let protocolData = res.data;
          this.protocolOptions = protocolData[this.formData.jrtype];
        }
      });
    },
    getServerList() {
      listServerAndSiteRequest().then((res) => {
        if (res.code === 200) {
          this.videoServerOptions = res.data;
        }
      });
    },
    getDivisionData() {
      let params = {
        isSync: false,
        pid: "",
      };
      QueryWholeDivisionTree4S(params).then((res) => {
        if (res.code === 200) {
          this.treeData = res.data.divisionTree;
          this.defaultExpandedKeys = this.treeData.map((d) => d.code);
        }
      });
    },
    changeAdHandle(node) {
      this.formData.adcd = (node && node.code) || "";
      this.formData.adcdName = (node && node.name) || "";
    },
    typeChangeHandle(value) {
      this.formData.cameraType = value;
    },
    selectChanges(node) {
      this.formData.orgId = node && node.id || ""
      this.formData.orgName = node && node.name || ""
    },
    transOptionsChange(value) {
      this.formData.transType = value || "";
    },
    saveFormData() {
      this.$refs["formData"].validate((valid) => {
        if (valid) {
          const loading = this.$loading({
            lock: true,
            text: "加载中",
            spinner: "el-icon-loading",
            background: "rgba(0, 0, 0, 0.7)",
          });
          UpdateSiteRequest(this.formData)
            .then((res) => {
              loading.close();
              if (res.code === 200) {
                this.$message({
                  type: "success",
                  message: "保存成功",
                });
                this.$emit("opFin");
              }
            })
            .catch((err) => {
              loading.close();
            });
        } else {
          console.log("error submit!!");
          return false;
        }
      });
    },
  },
};
</script>

<style>
</style>