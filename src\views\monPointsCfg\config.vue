<template>
  <div class="page-wapper">
    <div class="page-header">
      <el-page-header @back="goBack" :title="title" backIcon="el-icon-back">
      </el-page-header>
    </div>
    <div class="page-detail-box">
      <div class="form-header-box">
        <div class="form-header-title">AI算法配置</div>
        <div class="check-box">
          <el-checkbox
            v-model="formData.all"
            :indeterminate="formData.isIndeterminate"
            @change="allChange"
            style="margin-right: 24px"
            >全部</el-checkbox
          >
          <el-checkbox-group v-model="formData.config" @change="singleChange">
            <el-checkbox
              v-for="item in algorithmOptions"
              :key="item.code"
              :label="item.code"
              >{{ item.name }}</el-checkbox
            >
          </el-checkbox-group>
        </div>
      </div>
      <div class="ai-detail-box" v-if="tabsList.length">
        <div class="form-box">
          <el-tabs v-model="activeName">
            <el-tab-pane
              v-for="item in tabsList"
              :key="item.code"
              :label="item.name"
              :name="item.code"
            ></el-tab-pane>
          </el-tabs>
          <div class="form-left-box">
            <div class="video-box">
              <dse-video-player
                ref="videoRef"
                v-if="playerForm.cameraIndexCode"
                :cameraIndexCode="playerForm.cameraIndexCode"
                :keyIndex="index"
                :serverType="playerForm.jrtype"
                :name="playerForm.name"
                :transType="playerForm.transType"
                :isTool="false"
                :realtimeEnable="playerForm.realtimeEnable && isPlugins.AI"
                :isShowCollect="true"
                :isCollect="false"
                :pageTheme="pageTheme"
                :isClose="false"
              ></dse-video-player>
              <div class="canvas-box" v-if="isDrawing">
                <polygon-draw
                  v-if="activeName === 'person'"
                  ref="polygonRef"
                  :areaPoints="areaPoints"
                  @setData="setDrawData"
                ></polygon-draw>
                <parallel-draw
                  v-if="activeName === 'gate_opening'"
                  ref="parallelRef"
                  :areaPoints="gatePoints"
                  :currentGatage="currentGatage"
                  @setData="setGatageDrawData"
                ></parallel-draw>
              </div>
            </div>
          </div>
        </div>
        <div class="component-box">
          <template v-for="(item, index) in tabsList">
            <component
              :ref="'componentRef' + index"
              :key="index"
              v-show="activeName === item.code"
              :is="getComponent(item.code)"
              :data="item"
              :serverNode="playerForm"
              @control="controlHandle"
              @gotoPreset="gotoPresetHandle"
              @startDraw="startDrawHandle"
              @cancelDraw="cancelDrawHandle"
              @startParallelDraw="startParallelDrawHandle"
              @cancelParallelDraw="cancelParallelDrawHandle"
              @initGatage="initGatageHandle"
              @addGatage="addGatageHandle"
              @deleteGatage="deleteGatageHandle"
            ></component>
          </template>
        </div>
      </div>
      <el-empty
        description="请添加算法配置"
        v-if="tabsList.length === 0"
      ></el-empty>
    </div>
    <div class="page-detail-bottom" v-if="tabsList.length">
      <el-button @click="goBack">取消</el-button>
      <el-button type="primary" @click="saveHandle" :loading="loading"
        >保存</el-button
      >
    </div>
  </div>
</template>
<script>
import {
  algorithmListRequest,
  getAlgorithmCofRequest,
  saveOrUpdateAlgorithmRequest,
} from "@/api/ai/aiData";
import { pluginsCommonRequest } from "@/api/common/commonData";
import person from "./configComponents/person.vue";
import floater from "./configComponents/floater.vue";
import waterGauge from "./configComponents/waterGauge.vue";
import polygonDraw from "@/components/polygonDraw/index.vue";
import gatage from "./configComponents/gatage.vue";
import parallelDraw from "@/components/parallelDraw/index.vue";
export default {
  components: {
    person,
    floater,
    polygonDraw,
    waterGauge,
    gatage,
    parallelDraw,
  },
  data() {
    return {
      title: "AI算法配置",
      loading: false,
      algorithmOptions: [],
      tabsList: [],
      formData: {
        id: "",
        all: false,
        isIndeterminate: false,
        config: [],
      },
      formItem: {
        id: "",
        siteId: "",
        algorithmName: "",
        algorithmCode: "",
        algorithmType: "",
        alarmThreshold: null,
        alarmType: "",
        realtimeEnable: false,
        alarmEnable: false,
        dataSyncFreq: null,
        targets: "",
        upperLimit: null,
        lowerLimit: null,
        areaPoints: "",
        alarmTimes: "",
        remark: "",
        updateId: "",
        updateTime: "",
        refProjectId: ''
      },
      componentId: "person",
      playerForm: {
        cameraIndexCode: "",
        jrtype: "",
        transType: "",
        realtimeEnable: "",
        videoServerId: "",
      },
      activeName: "",
      pageTheme: "light",
      index: 1,
      isPlugins: {
        AI: false,
      },
      isDrawing: false,
      gatePoints: null,
      currentGatage: -1,
      componentMap: {
        person: "person",
        floater: "floater",
        water_gauge: "waterGauge",
        fishing: "floater",
        swimming: "floater",
        gate_opening: "gatage",
      },
      areaPoints: null,
    };
  },
  watch: {
    "formData.config": {
      handler() {
        this.tabsList = this.formData.config.map((d) => {
          let item = this.algorithmOptions.find((k) => k.code === d);
          let child = this.tabsList.find((tab) => d === tab.code);
          if (child) {
            return {
              ...this.formItem,
              ...item,
              ...child,
            };
          } else {
            return {
              ...this.formItem,
              ...item,
              algorithmCode: item && item.code,
              algorithmName: item && item.name,
              algorithmType: item && item.type,
            };
          }
        });
        const isActive = this.tabsList.some((d) => d.code === this.activeName);
        if (!isActive) {
          if (this.tabsList && this.tabsList.length > 0) {
            this.activeName = this.tabsList[0].code;
          } else {
            this.activeName = "";
          }
        }
        
        console.log(777, this.tabsList, this.formData.config)
      },
    },
    activeName() {
      if (this.activeName === "person" || this.activeName === "gate_opening") {
        this.isDrawing = true;
      } else {
        this.isDrawing = false;
      }
      console.log(456, this.activeName, this.isDrawing);
    },
  },
  mounted() {
    this.formData.id = this.$route.query.id || "";
    this.formItem.siteId = this.formData.id;
    this.playerForm.jrtype = this.$route.query.jrtype || "";
    this.playerForm.transType = this.$route.query.transType || "";
    this.playerForm.realtimeEnable = this.$route.query.realtimeEnable == "1";
    this.playerForm.videoServerId = this.$route.query.videoServerId;
    this.playerForm.cameraIndexCode = this.$route.query.cameraIndexCode || "";
    this.getAlgorithData();
  },
  methods: {
    getComponent(code) {
      return this.componentMap[code] || "floater";
    },
    getDetailData() {
      getAlgorithmCofRequest(this.formData.id).then((res) => {
        if (res.code === 200 && res.data) {
          let data = res.data;
          this.tabsList = data.map((d) => {
            return {
              ...d,
              name: d.algorithmName,
              code: d.algorithmCode,
            };
          });
          this.formData.config = res.data.map((d) => d.algorithmCode);
          let checkedCount = this.formData.config.length;
          this.formData.isIndeterminate =
        checkedCount > 0 && checkedCount < this.algorithmOptions.length;
          let item = this.tabsList.find((d) => d.code === "person");
          if (item && item.areaPoints && typeof item.areaPoints === "string") {
            this.areaPoints = JSON.parse(item.areaPoints);
          } else {
            this.areaPoints = null;
          }
        }
      });
    },
    getPluginsData() {
      pluginsCommonRequest().then((res) => {
        if (res.code === 200) {
          this.isPlugins = res.data || { AI: false };
        }
      });
    },
    getAlgorithData() {
      algorithmListRequest().then((res) => {
        if (res.code === 200) {
          let data = res.data;
          this.algorithmOptions = data;
          if (this.formData.id) {
            this.getDetailData();
          }
        }
      });
    },
    allChange() {
      if (this.formData.all) {
        this.formData.config = this.algorithmOptions.map((d) => d.code);
      } else {
        this.formData.config = [];
      }
      this.formData.isIndeterminate = false;
    },
    singleChange(value) {
      let checkedCount = value.length;
      this.formData.all = checkedCount === this.algorithmOptions.length;
      this.formData.isIndeterminate =
        checkedCount > 0 && checkedCount < this.algorithmOptions.length;
    },
    controlHandle(command, action, speed) {
      if (this.$refs.videoRef) {
        this.$refs.videoRef.controlHandle(command, action, speed);
      } else {
        this.$message({
          type: "warning",
          message: "请选择要控制的视频窗口",
        });
      }
    },
    gotoPresetHandle(node) {
      if (this.$refs.videoRef) {
        this.$refs.videoRef.gotoPresetHandle(node);
      } else {
        this.$message({
          type: "warning",
          message: "请选择要控制的视频窗口",
        });
      }
    },
    startDrawHandle() {
      this.isDrawing = true;
    },
    startParallelDrawHandle(index) {
      let item = this.gatePoints[index];
      if (item.points.length > 0) {
        this.$message.error("请先清除之前的绘制");
        return;
      }
      this.$refs.parallelRef && this.$refs.parallelRef.startDraw(index);
    },
    cancelParallelDrawHandle(index) {
      this.$set(this.gatePoints, index, {
        width: null,
        height: null,
        points: [],
      });
      console.log("cancelParallelDrawHandle", index, this.gatePoints);
    },
    initGatageHandle(data) {
      this.gatePoints = data.map((d) => d.points);
    },
    addGatageHandle() {
      this.gatePoints.push({
        width: null,
        height: null,
        points: [],
      });
    },
    deleteGatageHandle(index) {
      this.gatePoints.splice(index, 1);
    },
    cancelDrawHandle() {
      if (this.activeName === "person") {
        this.$refs.polygonRef && this.$refs.polygonRef.clearDraw();
        this.isDrawing = false;
        this.areaPoints = null;
        this.setDrawData();
      } else {
        this.$refs.parallelRef && this.$refs.parallelRef.clearDraw();
        this.isDrawing = false;
        this.gatePoints = [];
      }
    },
    setDrawData(data) {
      this.areaPoints = data;
      const index = this.tabsList.findIndex((d) => d.code === "person");
      if (this.$refs["componentRef" + index]) {
        this.$refs["componentRef" + index][0].setDrawData(data);
      }
    },
    setGatageDrawData(data) {
      let shapes = data.shapes;
      this.gatePoints = shapes.map((d) => {
        return {
          width: data.w,
          height: data.h,
          points: d.points,
        };
      });
      console.log(789, this.gatePoints);
    },
    saveHandle() {
      let promiseList = this.tabsList.map((d, index) => {
        return this.$refs["componentRef" + index][0].getData();
      });
      this.loading = true;
      Promise.all(promiseList)
        .then((data) => {
          let isError = false;
          data.forEach((item) => {
            if (item.algorithmCode === "gate_opening") {
              let errorItem = this.gatePoints.find((d) => d.points.length === 0);
              if (errorItem) {
                isError = true;
                return;
              }
              item.areaPointsList = item.areaPointsList.map((d, index) => {
                return {
                  ...d,
                  points: this.gatePoints[index],
                };
              });
              item.areaPoints = JSON.stringify(item.areaPointsList);
            }
          });
          if (isError) {
            this.$message.error("请绘制闸门参考线");
            this.loading = false;
            return;
          }
          saveOrUpdateAlgorithmRequest(data)
            .then((res) => {
              this.loading = false;
              if (res.code === 200) {
                this.$message({
                  type: "success",
                  message: "保存成功",
                });
                this.goBack();
              }
            })
            .catch(() => {
              this.loading = false;
            });
        })
        .catch(() => {
          this.loading = false;
        });
    },
    goBack() {
      this.$router.go(-1);
    },
  },
};
</script>
<style lang="scss" scoped>
.page-wapper {
  width: 100%;
  height: 100%;
  padding: 16px;
  box-sizing: border-box;
  background: #edf3fa;
}
.page-header {
  background: #fff;
}
.page-detail-box {
  height: calc(100% - 112px);
  box-sizing: border-box;
  overflow-y: auto;
  overflow-x: hidden;
}
.page-detail-bottom {
  height: 56px;
  display: flex;
  align-items: center;
  justify-content: flex-end;
  padding: 0 24px;
  box-sizing: border-box;
  background: #fff;
  border-radius: 4px;
}
.form-header-box {
  background: #fff;
  margin-top: 16px;
  margin-bottom: 16px;
  padding: 16px;
  box-sizing: border-box;
  border-radius: 4px;
}
.form-header-title {
  font-size: 16px;
  font-weight: 700;
  color: #151617;
  margin-bottom: 14px;
}
.check-box {
  display: flex;
}
.form-box {
  background: #fff;
  border-radius: 4px;
}
.ai-detail-box {
  display: flex;
  height: calc(100% - 130px);
}
.component-box {
  width: 300px;
  margin-left: 10px;
  height: 100%;
  overflow-y: auto;
  overflow-x: hidden;
}
.form-box {
  width: calc(100% - 310px);
}
.form-left-box {
  height: calc(100% - 62px);
  padding: 0 14px 14px;
  box-sizing: border-box;
}
.video-box {
  width: 100%;
  height: 100%;
  position: relative;
}
.canvas-box {
  width: 100%;
  height: 100%;
  position: absolute;
  top: 0;
  left: 0;
}
/deep/ .el-empty__description {
  margin-top: 20px;
}
</style>