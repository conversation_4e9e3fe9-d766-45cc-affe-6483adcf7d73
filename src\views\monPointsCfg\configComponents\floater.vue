<template>
  <div class="form-right-box">
    <title-cell title="配置参数">
      <div class="form-box">
        <el-form :model="formItem" ref="formRef" :rules="formRules" label-position="top">
          <div class="form-item-cell">
            <el-form-item label="" prop="realtimeEnable">
              <div class="form-label">
                <span class="form-red">*</span>
                <span class="form-label-text">AI实时监测</span>
                <el-tooltip
                  class="item"
                  effect="dark"
                  content="配置后实时在视频界面显示检测结果"
                  placement="top"
                >
                  <img
                    src="@/assets/images/icon/icon-feedback.png"
                    class="icon-feedback"
                    alt=""
                    srcset=""
                  />
                </el-tooltip>
              </div>
              <el-switch v-model="formItem.realtimeEnable"></el-switch>
            </el-form-item>

            <el-form-item label="" prop="alarmEnable">
              <div class="form-label">
                <span class="form-red">*</span>
                <span class="form-label-text">AI后台检测</span>
                <el-tooltip
                  class="item"
                  effect="dark"
                  content="配置后在后台监测并按照频率生成信息"
                  placement="top"
                >
                  <img
                    src="@/assets/images/icon/icon-feedback.png"
                    class="icon-feedback"
                    alt=""
                    srcset=""
                  />
                </el-tooltip>
              </div>
              <el-switch v-model="formItem.alarmEnable"></el-switch>
            </el-form-item>
          </div>
          <el-form-item label="预警等级" prop="alarmType">
            <pt-dict-down-list
              :selectItem="formItem.alarmType"
              :type="dictCodes.alarmType"
              :isDefault="false"
              :addAll="false"
              @change="typeChangeHandle"
            ></pt-dict-down-list>
          </el-form-item>
          <el-form-item label="" prop="alarmThreshold">
            <div class="form-label">
              <span class="form-red">*</span>
              <span class="form-label-text">预警精度阈值</span>
              <el-tooltip
                class="item"
                effect="dark"
                content="范围0~1，当监测精度超过配置项预警阈值, 自动生成预警信息"
                placement="top"
              >
                <img
                  src="@/assets/images/icon/icon-feedback.png"
                  class="icon-feedback"
                  alt=""
                  srcset=""
                />
              </el-tooltip>
            </div>
            <number-input v-model="formItem.alarmThreshold" :min="0" :max="1" :step="0.1" :precision="2" style="width: 100%"></number-input>
          </el-form-item>
        </el-form>
      </div>
    </title-cell>
    <title-cell title="监测时间">
      <time-range-box
        :list="formItem.alarmTimes"
        @change="rangeChangeHandle"
      ></time-range-box>
    </title-cell>
  </div>
</template>

<script>
import titleCell from "@/components/titleCell/index.vue";
import PtDictDownList from "@/components/PtDictDownList/index.vue";
import timeRangeBox from "./timeRangeBox.vue";
import NumberInput from "@/components/numberInput/index.vue"; 
export default {
  components: {
    titleCell,
    PtDictDownList,
    timeRangeBox,
    NumberInput,
  },
  props: {
    data: {
      type: Object,
      default: () => {},
    },
  },
  watch: {
    data: {
      handler() {
        this.init();
      },
      immediate: true,
    },
  },
  data() {
    return {
      pageTheme: "light",
      formItem: {
        realtimeEnable: true,
        alarmEnable: true,
        alarmType: "",
        alarmThreshold: "",
        alarmTimes: [],
        remark: "",
      },
      alarmTimes: [
        { start: "00:00", end: "23:59" },
        { start: "00:00", end: "23:59" },
        { start: "00:00", end: "23:59" },
        { start: "00:00", end: "23:59" },
        { start: "00:00", end: "23:59" },
        { start: "00:00", end: "23:59" },
        { start: "00:00", end: "23:59" },
      ],
      formRules: {
        realtimeEnable: [
          { required: true, message: "请选择AI实时监测", trigger: "blur" },
        ],
        alarmEnable: [
          { required: true, message: "请选择AI后台监测", trigger: "blur" },
        ],
        alarmType: [
          { required: true, message: "请选择预警等级", trigger: "blur" },
        ],
        alarmThreshold: [
          { required: true, message: "请输入阈值", trigger: "blur" },
        ],
      },
    };
  },
  methods: {
    init() {
      if (this.data) {
        this.formItem = this.data;
        if (this.data.alarmTimes) {
          if(typeof this.data.alarmTimes === 'string') {
            this.formItem.alarmTimes = JSON.parse(this.data.alarmTimes);
          } else {
            this.formItem.alarmTimes = this.data.alarmTimes;
          }
        } else {
          this.formItem.alarmTimes = this.alarmTimes;
        }
      }
    },
    typeChangeHandle(value) {
      this.formItem.alarmType = value || "";
    },
    rangeChangeHandle(index, range) {
      this.formItem.alarmTimes[index] = range;
    },
    getData() {
      return new Promise((resolve, reject) => {
        this.$refs.formRef.validate((vali) => {
          if (vali) {
            let params = {
              ...this.formItem,
            };
            params.alarmTimes = JSON.stringify(this.formItem.alarmTimes);
            resolve(params);
          } else {
            reject(this.formItem);
          }
        });
      });
    },
  },
};
</script>
<style lang="scss" scoped>
.form-right-box {
  width: 300px;
}
.title-cell-box {
  margin-bottom: 16px;
}
.title-cell-box:last-child {
  margin-bottom: 0;
}
.btn-box {
  display: flex;
  padding: 16px;
  box-sizing: border-box;
}
.form-box {
  padding: 16px;
  box-sizing: border-box;
}
.icon-feedback {
  width: 12px;
  height: 12px;
}
.form-label {
  color: #666666;
}
.form-label-text {
  margin-right: 5px;
}
.form-red {
  color: #f56c6c;
}
.form-item-cell {
  display: flex;
  justify-content: space-between;
}
.form-item-cell {
  /deep/ .el-form-item {
    flex: 1;
  }
}
</style>