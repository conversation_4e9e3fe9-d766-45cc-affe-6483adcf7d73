<template>
  <div class="form-right-box">
    <el-form
      ref="formRef"
      :model="formItem"
      :rules="formRules"
      label-position="top"
    >
      <title-cell title="配置参数">
        <div class="form-box">
          <div class="form-item-cell">
            <el-form-item label="" prop="realtimeEnable">
              <div class="form-label">
                <span class="form-red">*</span>
                <span class="form-label-text">AI实时监测</span>
                <el-tooltip
                  class="item"
                  effect="dark"
                  content="配置后实时在视频界面显示检测结果"
                  placement="top"
                >
                  <img
                    src="@/assets/images/icon/icon-feedback.png"
                    class="icon-feedback"
                    alt=""
                    srcset=""
                  />
                </el-tooltip>
              </div>
              <el-switch v-model="formItem.realtimeEnable"></el-switch>
            </el-form-item>

            <el-form-item label="" prop="alarmEnable">
              <div class="form-label">
                <span class="form-red">*</span>
                <span class="form-label-text">AI后台检测</span>
                <el-tooltip
                  class="item"
                  effect="dark"
                  content="配置后在后台监测并按照频率生成信息"
                  placement="top"
                >
                  <img
                    src="@/assets/images/icon/icon-feedback.png"
                    class="icon-feedback"
                    alt=""
                    srcset=""
                  />
                </el-tooltip>
              </div>
              <el-switch v-model="formItem.alarmEnable"></el-switch>
            </el-form-item>
          </div>
          <el-form-item label="关联工程" prop="refProjectId">
            <el-select
              v-model="formItem.refProjectId"
              placeholder="请选择关联工程"
              style="width: 100%"
            >
              <el-option
                v-for="item in projectList"
                :key="item.id"
                :label="item.label"
                :value="item.id"
              ></el-option>
            </el-select>
          </el-form-item>
          <el-form-item label="检测间隔频率（秒）" prop="dataSyncFreq">
            <number-input
              v-model="formItem.dataSyncFreq"
              :min="0"
              :max="9999999"
              :step="1"
              :decimal="1"
              style="width: 100%"
            ></number-input> </el-form-item
          ><el-form-item label="预警阈值上限(m)" prop="upperLimit">
            <number-input
              v-model="formItem.upperLimit"
              :min="0"
              :max="9999999"
              :step="1"
              :precision="3"
              style="width: 100%"
            ></number-input> </el-form-item
          ><el-form-item label="预警阈值下限(m)" prop="lowerLimit">
            <number-input
              v-model="formItem.lowerLimit"
              :min="0"
              :max="9999999"
              :step="1"
              :precision="3"
              style="width: 100%"
            ></number-input>
          </el-form-item>
        </div>
      </title-cell>
      <title-cell title="闸门配置">
        <template #headerDesc>
          <el-button type="primary" size="mini" @click="addGatage"
            >新增闸门</el-button
          >
        </template>
        <div class="form-box">
          <el-collapse v-model="activeNames" icon-position="left" border>
            <el-collapse-item
              v-for="(item, index) in formItem.areaPointsList"
              :key="index"
              :title="`${index + 1}#闸门`"
              :name="index"
            >
              <template slot="title">
                <div class="gatage-item-title">
                  <span>{{ index + 1 }}#闸门</span>
                  <el-link
                    type="danger"
                    size="mini"
                    v-if="formItem.areaPointsList.length > 1"
                    @click.stop="deleteGatage(index)"
                    >删除</el-link
                  >
                </div>
              </template>

              <div class="gatage-item">
                <el-form-item label="绘制参考线" prop="gatagePosition">
                  <el-button
                    type="primary"
                    size="mini"
                    @click="startDraw(item, index)"
                    >开始绘制</el-button
                  >
                  <el-button size="mini" @click="resetDraw(item, index)"
                    >清除绘制</el-button
                  >
                </el-form-item>
                <el-form-item
                  label="闸门名称"
                  :prop="`areaPointsList[${index}].name`"
                  :rules="formRules.name"
                >
                  <el-input
                    v-model="item.name"
                    placeholder="请输入闸门名称"
                  ></el-input>
                </el-form-item>
                <el-form-item
                  label="参考线距离(米)"
                  :prop="`areaPointsList[${index}].h`"
                  :rules="formRules.h"
                >
                  <number-input
                    v-model="item.h"
                    :min="0"
                    :max="9999999"
                    :step="1"
                    :precision="2"
                    style="width: 100%"
                  ></number-input>
                </el-form-item>
                <el-form-item
                  label="闸门的总行程(米)"
                  :prop="`areaPointsList[${index}].y`"
                  :rules="formRules.y"
                >
                  <number-input
                    v-model="item.y"
                    :min="0"
                    :max="9999999"
                    :step="1"
                    :precision="2"
                    style="width: 100%"
                  ></number-input>
                </el-form-item>
              </div>
            </el-collapse-item>
          </el-collapse>
        </div>
      </title-cell>
      <title-cell title="监测时间">
        <time-range-box
          :list="formItem.alarmTimes"
          @change="rangeChangeHandle"
        ></time-range-box>
      </title-cell>
    </el-form>
  </div>
</template>
    
    <script>
import titleCell from "@/components/titleCell/index.vue";
import NumberInput from "@/components/numberInput/index.vue";
import timeRangeBox from "./timeRangeBox.vue";
import { projectListRequest } from '@/api/common/commonData';

export default {
  components: {
    titleCell,
    timeRangeBox,
    NumberInput,
  },
  props: {
    data: {
      type: Object,
      default: () => {},
    },
  },
  watch: {
    data: {
      handler() {
        this.init();
      },
      immediate: true,
    },
  },
  data() {
    return {
      pageTheme: "light",
      formItem: {
        realtimeEnable: true,
        alarmEnable: true,
        alarmType: "",
        alarmThreshold: "",
        alarmTimes: [],
        remark: "",
        dataSyncFreq: null,
        upperLimit: null,
        lowerLimit: null,
        areaPoints: null,
        refProjectId: '',
        areaPointsList: [
          {
            name: "闸门1",
            h: "",
            y: "",
            points: {}, // [[{x: 0, y: 0}, {x: 0, y: 0}], [{x: 0, y: 0}, {x: 0, y: 0}]]
          },
        ],
      },
      activeNames: [0],
      alarmTimes: [
        { start: "00:00", end: "23:59" },
        { start: "00:00", end: "23:59" },
        { start: "00:00", end: "23:59" },
        { start: "00:00", end: "23:59" },
        { start: "00:00", end: "23:59" },
        { start: "00:00", end: "23:59" },
        { start: "00:00", end: "23:59" },
      ],
      formRules: {
        realtimeEnable: [
          { required: true, message: "请选择AI实时监测", trigger: "blur" },
        ],
        alarmEnable: [
          { required: true, message: "请选择AI后台监测", trigger: "blur" },
        ],
        refProjectId: [
          { required: true, message: "请选择关联工程", trigger: "blur" },
        ],
        dataSyncFreq: [
          { required: true, message: "请输入检测间隔频率", trigger: "blur" },
        ],
        name: [{ required: true, message: "请输入闸门名称", trigger: "blur" }],
        h: [{ required: true, message: "请输入参考线距离", trigger: "blur" }],
        y: [{ required: true, message: "请输入闸门的总行程", trigger: "blur" }],
      },
      projectList: [],
    };
  },
  mounted() {
    this.getProjectList()
  },
  methods: {
    init() {
      if (this.data) {
        this.formItem = this.data;
        // this.formItem.dataSyncFreq = this.getSyncFreq(this.data.dataSyncFreq);
        if (this.data.alarmTimes) {
          if (typeof this.data.alarmTimes === "string") {
            this.formItem.alarmTimes = JSON.parse(this.data.alarmTimes);
          } else {
            this.formItem.alarmTimes = this.data.alarmTimes;
          }
        } else {
          this.formItem.alarmTimes = this.alarmTimes;
        }
        if (this.data.areaPoints && typeof this.data.areaPoints === "string") {
          this.$set(
            this.formItem,
            "areaPointsList",
            JSON.parse(this.data.areaPoints)
          );
        } else {
          this.$set(this.formItem, "areaPointsList", [
            {
              name: "",
              h: "",
              y: "",
              points: {},
            },
          ]);
        }
        this.$emit('initGatage', this.formItem.areaPointsList)
      }
    },
    getProjectList() { 
      projectListRequest().then(res => {
        if (res.code === 200) {
          this.projectList = res.data
        } else {
          this.projectList = []
        }
      })
    },
    getSyncFreq(value) {
      if (value === null) {
        return null;
      }
      const rawResult = value / 60;
      const result = Math.round(rawResult * 100) / 100;
      return result;
    },
    typeChangeHandle(value) {
      this.formItem.alarmType = value || "";
    },
    rangeChangeHandle(index, range) {
      this.formItem.alarmTimes[index] = range;
    },
    addGatage() {
      this.formItem.areaPointsList.push({
        name: "",
        h: "",
        y: "",
        points: {},
      });
      this.$emit('addGatage')
    },
    deleteGatage(index) {
      this.formItem.areaPointsList.splice(index, 1);
      this.$emit('deleteGatage', index)
    },
    startDraw(item, index) {
      this.$emit("startParallelDraw", index, this.formItem.areaPointsList);
    },
    resetDraw(item, index) {
      this.$emit("cancelParallelDraw", index);
    },
    getData() {
      return new Promise((resolve, reject) => {
        this.$refs.formRef.validate((vali) => {
          if (vali) {
            if (
              this.formItem.upperLimit &&
              Number(this.formItem.upperLimit) < Number(this.formItem.lowerLimit)
            ) {
              this.$message.error("预警阈值上限不能小于预警阈值下限");
              reject();
              return;
            }
            let params = {
              ...this.formItem,
            };
            params.alarmTimes = JSON.stringify(this.formItem.alarmTimes);
            params.dataSyncFreq = params.dataSyncFreq * 1;
            params.alarmType = this.formItem.alarmType || "1";
            resolve(params);
          } else {
            reject(this.formItem);
          }
        });
      });
    },
  },
};
</script>
    <style lang="scss" scoped>
.form-right-box {
  width: 300px;
}
.title-cell-box {
  margin-bottom: 16px;
}
.title-cell-box:last-child {
  margin-bottom: 0;
}
.btn-box {
  display: flex;
  padding: 16px;
  box-sizing: border-box;
}
.form-box {
  padding: 16px;
  box-sizing: border-box;
}
.icon-feedback {
  width: 12px;
  height: 12px;
}
.form-label {
  color: #666666;
}
.form-label-text {
  margin-right: 5px;
}
.form-red {
  color: #f56c6c;
}
.form-item-cell {
  display: flex;
  justify-content: space-between;
}
.form-item-cell {
  /deep/ .el-form-item {
    flex: 1;
  }
}
.gatage-item-title {
  display: flex;
  justify-content: space-between;
  align-items: center;
  flex: 1;
  padding-right: 12px;
  box-sizing: border-box;
}
</style>