<template>
  <div class="form-right-box">
    <title-cell title="云台控制">
      <real-tool
        ref="realToolRef"
        :pageTheme="pageTheme"
        @control="controlHandle"
        @gotoPreset="gotoPresetHandle"
      ></real-tool>
    </title-cell>

    <title-cell title="绘制区域" desc="若不配置区域，默认全屏识别">
      <div class="draw-box">
        <div class="btn-box">
          <el-button type="primary" style="flex: 1" @click="startDraw"
            >绘制监测区域</el-button
        >
        <el-button type="primary" plain style="flex: 1" @click="cancelDraw"
          >取消</el-button
        >
      </div>
      <div class="dec-text">
        注：鼠标右键结束绘制
      </div>
    </div>
    </title-cell>
    <title-cell title="配置参数">
      <div class="form-box">
        <el-form
          :model="formItem"
          ref="formRef"
          :rules="formRules"
          label-position="top"
        >
          <div class="form-item-cell">
            <el-form-item label="" prop="realtimeEnable">
              <div class="form-label">
                <span class="form-red">*</span>
                <span class="form-label-text">AI实时监测</span>
                <el-tooltip
                  class="item"
                  effect="dark"
                  content="配置后实时在视频界面显示检测结果"
                  placement="top"
                >
                  <img
                    src="@/assets/images/icon/icon-feedback.png"
                    class="icon-feedback"
                    alt=""
                    srcset=""
                  />
                </el-tooltip>
              </div>
              <el-switch v-model="formItem.realtimeEnable"></el-switch>
            </el-form-item>

            <el-form-item label="" prop="alarmEnable">
              <div class="form-label">
                <span class="form-red">*</span>
                <span class="form-label-text">AI后台检测</span>
                <el-tooltip
                  class="item"
                  effect="dark"
                  content="配置后在后台监测并按照频率生成信息"
                  placement="top"
                >
                  <img
                    src="@/assets/images/icon/icon-feedback.png"
                    class="icon-feedback"
                    alt=""
                    srcset=""
                  />
                </el-tooltip>
              </div>
              <el-switch v-model="formItem.alarmEnable"></el-switch>
            </el-form-item>
          </div>
          <el-form-item label="监测目标" prop="targets">
            <el-checkbox-group v-model="formItem.targets">
              <el-checkbox
                v-for="item in targetsList"
                :key="item.value"
                :label="item.value"
                >{{ item.label }}</el-checkbox
              >
            </el-checkbox-group>
          </el-form-item>
          <el-form-item label="预警等级" prop="alarmType">
            <pt-dict-down-list
              :selectItem="formItem.alarmType"
              :type="dictCodes.alarmType"
              :isDefault="false"
              :addAll="false"
              @change="typeChangeHandle"
            ></pt-dict-down-list>
          </el-form-item>
          <el-form-item label="" prop="alarmThreshold">
            <div class="form-label">
              <span class="form-red">*</span>
              <span class="form-label-text">预警精度阈值</span>
              <el-tooltip
                class="item"
                effect="dark"
                content="范围0~1，当监测精度超过配置项预警阈值, 自动生成预警信息"
                placement="top"
              >
                <img
                  src="@/assets/images/icon/icon-feedback.png"
                  class="icon-feedback"
                  alt=""
                  srcset=""
                />
              </el-tooltip>
            </div>
            <number-input v-model="formItem.alarmThreshold" :min="0" :max="1" :step="0.1" :precision="3" style="width: 100%"></number-input>
          </el-form-item>
        </el-form>
      </div>
    </title-cell>
    <title-cell title="监测时间">
      <time-range-box
        :list="formItem.alarmTimes"
        @change="rangeChangeHandle"
      ></time-range-box>
    </title-cell>
  </div>
</template>
<script>
import realTool from "@/views/videoView/components/realTool.vue";
import titleCell from "@/components/titleCell/index.vue";
import PtDictDownList from "@/components/PtDictDownList/index.vue";
import NumberInput from "@/components/numberInput/index.vue"; 
import timeRangeBox from "./timeRangeBox.vue";
export default {
  components: {
    realTool,
    titleCell,
    PtDictDownList,
    timeRangeBox,
    NumberInput,
  },
  props: {
    data: {
      type: Object,
      default: () => {},
    },
    serverNode: {
      type: Object,
      default: () => {},
    },
  },
  watch: {
    data: {
      handler() {
        this.init();
      },
      immediate: true,
    },
    "serverNode.cameraIndexCode": {
      handler() {
        this.getPresetData();
      },
      immediate: true,
      deep: true,
    },
  },
  data() {
    return {
      pageTheme: "light",
      formItem: {
        realtimeEnable: true,
        alarmEnable: true,
        alarmType: "",
        targets: [],
        alarmThreshold: null,
        areaPoints: null,
        alarmTimes: [],
        remark: "",
      },
      alarmTimes: [
        { start: "00:00", end: "23:59" },
        { start: "00:00", end: "23:59" },
        { start: "00:00", end: "23:59" },
        { start: "00:00", end: "23:59" },
        { start: "00:00", end: "23:59" },
        { start: "00:00", end: "23:59" },
        { start: "00:00", end: "23:59" },
      ],
      targetsList: [
        { label: "人体", value: "person" },
        { label: "车辆", value: "car" },
      ],
      formRules: {
        realtimeEnable: [
          { required: true, message: "请选择AI实时监测", trigger: "blur" },
        ],
        alarmEnable: [
          { required: true, message: "请选择AI后台监测", trigger: "blur" },
        ],
        targets: [
          { required: true, message: "请选择监测目标", trigger: "blur" },
        ],
        alarmType: [
          { required: true, message: "请选择预警等级", trigger: "blur" },
        ],
        alarmThreshold: [
          { required: true, message: "请输入阈值", trigger: "blur" },
        ],
      },
    };
  },
  methods: {
    init() {
      if (this.data) {
        this.formItem = this.data;
        this.formItem.targets = this.getTargets();
        if (this.data.alarmTimes) {
          if (typeof this.data.alarmTimes === "string") {
            this.formItem.alarmTimes = JSON.parse(this.data.alarmTimes);
          } else {
            this.formItem.alarmTimes = this.data.alarmTimes;
          }
        } else {
          this.formItem.alarmTimes = this.alarmTimes;
        }
      }
      this.$nextTick(() => {
        this.getPresetData();
      });
    },
    getTargets() {
      let targets = this.data.targets;
      if (Array.isArray(targets)) {
        return targets;
      } else if (typeof targets === "string") {
        return targets.split(",");
      } else {
        return [];
      }
    },
    controlHandle(command, action, speed) {
      this.$emit("control", command, action, speed);
    },
    gotoPresetHandle(preset) {
      this.$emit("gotoPreset", preset);
    },
    typeChangeHandle(value) {
      this.formItem.alarmType = value || "";
    },
    rangeChangeHandle(index, range) {
      this.formItem.alarmTimes[index] = range;
    },
    startDraw() {
      this.$emit("startDraw");
    },
    cancelDraw() {
      this.$emit("cancelDraw");
    },
    setDrawData(data) {
      this.formItem.areaPoints = data ? JSON.stringify(data) : "";
    },
    getPresetData() {
      if (
        this.serverNode &&
        this.serverNode.cameraIndexCode &&
        this.$refs.realToolRef
      ) {
        this.$refs.realToolRef.getPresetHandle(this.serverNode);
      }
    },
    getData() {
      return new Promise((resolve, reject) => {
        this.$refs.formRef.validate((vali) => {
          if (vali) {
            let params = {
              ...this.formItem,
            };
            params.alarmTimes = JSON.stringify(this.formItem.alarmTimes);
            params.targets = this.formItem.targets.join(",");
            resolve(params);
          } else {
            reject(this.formItem);
          }
        });
      });
    },
  },
};
</script>
<style lang="scss" scoped>
.form-right-box {
  width: 300px;
}
.title-cell-box {
  margin-bottom: 16px;
}
.title-cell-box:last-child {
  margin-bottom: 0;
}
.draw-box {
  padding: 16px;
}
.btn-box {
  display: flex;
  box-sizing: border-box;
}
.dec-text {
  margin-top: 10px;
  color: #666;
}
.form-box {
  padding: 16px;
  box-sizing: border-box;
}
.icon-feedback {
  width: 12px;
  height: 12px;
}
.form-label {
  color: #666666;
}
.form-label-text {
  margin-right: 5px;
}
.form-red {
  color: #f56c6c;
}
.form-item-cell {
  display: flex;
  justify-content: space-between;
}
.form-item-cell {
  /deep/ .el-form-item {
    flex: 1;
  }
}
</style>