<template>
  <div class="time-range-box">
    <div class="time-range-item" v-for="(item, index) in list" :key="index" >
      <div class="time-range-item-title">星期{{ getTitile(index) }}</div>
      <div class="slider-item">
        <time-range :range="item" @change="$event => rangeChange(index, $event)" :key="index"></time-range>
      </div>
    </div>
  </div>
</template>
<script>
import timeRange from '@/components/timeRange/index.vue'
export default {
  name: 'TimeRangeBox',
  components: {
    timeRange
  },
  props: {
    list: {
      type: Array,
      default: () => []
    }
  },
  data() {
    return {
    }
  },
  methods: {
    getTitile(index) {
      return ['一', '二', '三', '四', '五', '六', '日'][index]
    },
    rangeChange(index, range) {
      this.$emit('change', index, range)  
    }
  }
}
</script>
<style lang="scss" scoped>
.time-range-box {
  padding: 16px 16px 10px;
  box-sizing: border-box;
}
.time-range-item {
  display: flex;
  align-items: center;
  margin-bottom: 10px;
}
.time-range-item-title {
  width: 50px;
}
.slider-item {
  width: calc(100% - 90px);
  margin-left: 20px;
  margin-top: 20px;
}
</style>