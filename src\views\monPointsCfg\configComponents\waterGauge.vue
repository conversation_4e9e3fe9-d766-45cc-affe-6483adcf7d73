<template>
  <div class="form-right-box">
    <title-cell title="配置参数">
      <div class="form-box">
        <el-form
          ref="formRef"
          :model="formItem"
          :rules="formRules"
          label-position="top"
        >
          <div class="form-item-cell">
            <el-form-item label="" prop="realtimeEnable">
              <div class="form-label">
                <span class="form-red">*</span>
                <span class="form-label-text">AI实时监测</span>
                <el-tooltip
                  class="item"
                  effect="dark"
                  content="配置后实时在视频界面显示检测结果"
                  placement="top"
                >
                  <img
                    src="@/assets/images/icon/icon-feedback.png"
                    class="icon-feedback"
                    alt=""
                    srcset=""
                  />
                </el-tooltip>
              </div>
              <el-switch v-model="formItem.realtimeEnable"></el-switch>
            </el-form-item>

            <el-form-item label="" prop="alarmEnable">
              <div class="form-label">
                <span class="form-red">*</span>
                <span class="form-label-text">AI后台检测</span>
                <el-tooltip
                  class="item"
                  effect="dark"
                  content="配置后在后台监测并按照频率生成信息"
                  placement="top"
                >
                  <img
                    src="@/assets/images/icon/icon-feedback.png"
                    class="icon-feedback"
                    alt=""
                    srcset=""
                  />
                </el-tooltip>
              </div>
              <el-switch v-model="formItem.alarmEnable"></el-switch>
            </el-form-item>
          </div>
          <el-form-item label="检测间隔频率（秒）" prop="dataSyncFreq">
            <number-input
              v-model="formItem.dataSyncFreq"
              :min="0"
              :max="9999999"
              :step="1"
              :decimal="1"
              style="width: 100%"
            ></number-input> </el-form-item
          ><el-form-item label="预警阈值上限(m)" prop="upperLimit">
            <number-input
              v-model="formItem.upperLimit"
              :min="0"
              :max="9999999"
              :step="1"
              :precision="3"
              style="width: 100%"
            ></number-input> </el-form-item
          ><el-form-item label="预警阈值下限(m)" prop="lowerLimit">
            <number-input
              v-model="formItem.lowerLimit"
              :min="0"
              :max="9999999"
              :step="1"
              :precision="3"
              style="width: 100%"
            ></number-input>
          </el-form-item>
        </el-form>
      </div>
    </title-cell>
    <title-cell title="监测时间">
      <time-range-box
        :list="formItem.alarmTimes"
        @change="rangeChangeHandle"
      ></time-range-box>
    </title-cell>
  </div>
</template>
  
  <script>
import titleCell from "@/components/titleCell/index.vue";
import NumberInput from "@/components/numberInput/index.vue";
import timeRangeBox from "./timeRangeBox.vue";
export default {
  components: {
    titleCell,
    timeRangeBox,
    NumberInput,
  },
  props: {
    data: {
      type: Object,
      default: () => {},
    },
  },
  watch: {
    data: {
      handler() {
        this.init();
      },
      immediate: true,
    },
  },
  data() {
    return {
      pageTheme: "light",
      formItem: {
        realtimeEnable: true,
        alarmEnable: true,
        alarmType: "",
        alarmThreshold: "",
        alarmTimes: [],
        remark: "",
        dataSyncFreq: null,
        upperLimit: null,
        lowerLimit: null,
      },
      alarmTimes: [
        { start: "00:00", end: "23:59" },
        { start: "00:00", end: "23:59" },
        { start: "00:00", end: "23:59" },
        { start: "00:00", end: "23:59" },
        { start: "00:00", end: "23:59" },
        { start: "00:00", end: "23:59" },
        { start: "00:00", end: "23:59" },
      ],
      formRules: {
        realtimeEnable: [
          { required: true, message: "请选择AI实时监测", trigger: "blur" },
        ],
        alarmEnable: [
          { required: true, message: "请选择AI后台监测", trigger: "blur" },
        ],
        dataSyncFreq: [
          { required: true, message: "请输入检测间隔频率", trigger: "blur" },
        ],
      },
    };
  },

  methods: {
    init() {
      if (this.data) {
        this.formItem = this.data;
        // this.formItem.dataSyncFreq = this.getSyncFreq(this.data.dataSyncFreq);
        if (this.data.alarmTimes) {
          if (typeof this.data.alarmTimes === "string") {
            this.formItem.alarmTimes = JSON.parse(this.data.alarmTimes);
          } else {
            this.formItem.alarmTimes = this.data.alarmTimes;
          }
        } else {
          this.formItem.alarmTimes = this.alarmTimes;
        }
      }
    },
    getSyncFreq(value) {
      if(value === null) {
        return null;
      }
      const rawResult = value / 60;
      const result = Math.round(rawResult * 100) / 100;
      return result;
    },
    typeChangeHandle(value) {
      this.formItem.alarmType = value || "";
    },
    rangeChangeHandle(index, range) {
      this.formItem.alarmTimes[index] = range;
    },
    getData() {
      return new Promise((resolve, reject) => {
        this.$refs.formRef.validate((vali) => {
          if (vali) {
            if(this.formItem.upperLimit && Number(this.formItem.upperLimit) < Number(this.formItem.lowerLimit)) {
              this.$message.error("预警阈值上限不能小于预警阈值下限");
              reject();
              return;
            }
            let params = {
              ...this.formItem,
            };
            params.alarmTimes = JSON.stringify(this.formItem.alarmTimes);
            params.dataSyncFreq = params.dataSyncFreq * 1;
            params.alarmType = this.formItem.alarmType || '1';
            resolve(params);
          } else {
            reject(this.formItem);
          }
        });
      });
    },
  },
};
</script>
  <style lang="scss" scoped>
.form-right-box {
  width: 300px;
}
.title-cell-box {
  margin-bottom: 16px;
}
.title-cell-box:last-child {
  margin-bottom: 0;
}
.btn-box {
  display: flex;
  padding: 16px;
  box-sizing: border-box;
}
.form-box {
  padding: 16px;
  box-sizing: border-box;
}
.icon-feedback {
  width: 12px;
  height: 12px;
}
.form-label {
  color: #666666;
}
.form-label-text {
  margin-right: 5px;
}
.form-red {
  color: #f56c6c;
}
.form-item-cell {
  display: flex;
  justify-content: space-between;
}
.form-item-cell {
  /deep/ .el-form-item {
    flex: 1;
  }
}
</style>