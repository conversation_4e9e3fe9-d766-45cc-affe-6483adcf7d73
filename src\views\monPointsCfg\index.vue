<template>
  <div class="page-wrapper">
    <div class="wrapper-box" ref="wrapperRef">
      <div class="page-header-main">
        <div class="header-btn-box">
          <div>
            <el-button @click="addHandle" type="primary" icon="el-icon-plus"
              >新增</el-button
            >
            <el-button @click="deleteAllHandle" icon="el-icon-delete"
              >批量删除</el-button
            >
          </div>
          <el-input
            placeholder="请输入监控点名称"
            v-model="searchItem.name"
            style="width: 300px"
          >
          </el-input>
        </div>
        <div class="header-search-box">
          <el-form
            :model="searchItem"
            ref="formRef"
            label-width="112px"
            :inline="true"
          >
            <div class="from-box">
              <el-form-item label="服务器:">
                <el-select
                  v-model="searchItem.videoServerId"
                  placeholder="请选择服务器"
                  clearable
                  style="width: 100%"
                >
                  <el-option
                    v-for="item in serverOptions"
                    :key="item.id"
                    :label="item.name"
                    :value="item.id"
                  >
                  </el-option>
                </el-select>
              </el-form-item>
              <el-form-item label="监控点类型:">
                <pt-dict-down-list
                  :selectItem="searchItem.cameraType"
                  :type="dictCodes.cameraType"
                  :addAll="true"
                  @change="typeOptionsChange"
                ></pt-dict-down-list>
              </el-form-item>
              <el-form-item label="行政区划:">
                <el-cascader
                  :props="treeProps"
                  v-model="adcd"
                  :options="treeData"
                  style="width: 100%"
                  clearable
                  :show-all-levels="false"
                  @change="changeHandle"
                ></el-cascader>
              </el-form-item>
              <el-form-item label="管理单位:">
                <dse-org-select
                  @changes="selectChanges"
                  :value="orgId"
                ></dse-org-select>
              </el-form-item>
            </div>
          </el-form>
          <div class="search-btn-box">
            <el-button @click="resetHandle">重置</el-button>
            <el-button @click="handleSearch" type="primary">查询</el-button>
          </div>
        </div>
      </div>
      <div class="statistics-box">
        <div class="statistics-item">
          监控站总数<span class="statistics-num">{{
            countObject.totalQty
          }}</span
          >个，
        </div>
        <div class="statistics-item">
          球机<span class="statistics-num">{{ countObject.sphereQty }}</span
          >个，
        </div>
        <div class="statistics-item">
          枪机<span class="statistics-num">{{ countObject.gunQty }}</span
          >个，
        </div>
        <div class="statistics-item">
          半球<span class="statistics-num">{{ countObject.domeQty }}</span
          >个，
        </div>
        <div class="statistics-item">
          云台枪机<span class="statistics-num">{{ countObject.ptzGunQty }}</span
          >个，
        </div>
        <div class="statistics-item">
          快球<span class="statistics-num">{{ countObject.speedDomeQty }}</span
          >个，
        </div>
        <div class="statistics-item">
          本地采集输入<span class="statistics-num">{{
            countObject.localCaptureQty
          }}</span
          >个
        </div>
      </div>
      <div class="content-box" ref="contentRef">
        <el-table
          ref="table"
          v-loading="loading"
          element-loading-text="数据加载中..."
          element-loading-spinner="el-icon-loading"
          element-loading-background="rgba(0, 0, 0, 0.8)"
          border
          stripe
          tooltip-effect="light"
          row-key="id"
          :max-height="tableHeight"
          :data="tableData"
          @selection-change="handleSelectionChange"
        >
          <el-table-column type="selection" width="55" reserve-selection>
          </el-table-column>
          <el-table-column label="序号" align="center" width="70">
            <template slot-scope="scope">
              <span>
                {{
                  (searchItem.pageNum - 1) * searchItem.pageSize +
                  scope.$index +
                  1
                }}
              </span>
            </template>
          </el-table-column>
          <el-table-column
            prop="name"
            label="监控点名称"
            show-overflow-tooltip
            align="left"
          >
          </el-table-column>
          <el-table-column
            prop="videoServerName"
            label="视频服务器"
            show-overflow-tooltip
            align="left"
          >
          </el-table-column>
          <el-table-column
            prop="adcdName"
            label="行政区划"
            show-overflow-tooltip
            align="left"
          >
          </el-table-column>
          <el-table-column
            prop="orgName"
            label="管理单位"
            show-overflow-tooltip
            align="left"
          >
          </el-table-column>
          <el-table-column
            prop="cameraTypeName"
            label="监控点类型"
            show-overflow-tooltip
            align="left"
          >
          </el-table-column>
          <el-table-column
            prop="transTypeName"
            label="拉流方式"
            show-overflow-tooltip
            align="left"
          >
          </el-table-column>
          <el-table-column
            prop="alarmEnable"
            label="AI算法"
            show-overflow-tooltip
            align="left"
            v-if="isPlugins.AI"
          >
            <template slot-scope="scope">
              <span
                v-if="scope.row.aiConfList && scope.row.aiConfList.length > 0"
                >已配置</span
              >
              <span v-else>未配置</span>
            </template>
          </el-table-column>
          <el-table-column
            prop="remark"
            label="备注"
            show-overflow-tooltip
            align="left"
          >
          </el-table-column>
          <el-table-column
            prop="oper"
            fixed="right"
            label="操作"
            align="right"
            width="280px"
          >
            <template slot-scope="scope">
              <span
                @click="confHandle(scope.row)"
                class="oper-btn"
                v-if="isPlugins.AI"
              >
                AI算法配置
              </span>
              <span
                @click="openStatusHandle(scope.row)"
                class="oper-btn"
                v-if="isPlugins.AI"
              >
                算法状态
              </span>
              <span @click="editHandle(scope.row)" class="oper-btn">
                编辑
              </span>
              <span @click="handelDelete([scope.row.id])" class="oper-btn">
                删除
              </span>
            </template>
          </el-table-column>
        </el-table>
      </div>
      <div class="pager-box" ref="pager">
        <el-pagination
          @size-change="handleSizeChange"
          @current-change="handleCurrentChange"
          :current-page="searchItem.pageNum"
          :page-sizes="pageSizes"
          :page-size="searchItem.pageSize"
          layout="total, ->, prev, pager,next, sizes, jumper"
          :total="total"
        >
        </el-pagination>
      </div>
    </div>
    <site-dialog
      v-if="showDialog"
      :operaType="operaType"
      :itemData="editData"
      @success="saveSuccess"
      @close="closeHandle"
    ></site-dialog>
    <status-ai-dialog
      v-if="showStatusConf"
      :id="confId"
      @close="closeConfHandle"
      @success="confSuccessHandle"
    ></status-ai-dialog>
  </div>
</template>

<script>
import { QueryServerListRequest } from "@/api/server/serverData";
import {
  QueryWholeDivisionTree4S,
  QUERY_ORGANIZATION_TREE_4S_URL,
} from "@/api/baseInfo/baseInfoData";
import {
  PageSiteRequest,
  CountSiteRequest,
  DeleteSiteRequest,
} from "@/api/site/siteData";
import { pluginsCommonRequest } from "@/api/common/commonData";
import { realtimeConfRequest, alarmConfRequest } from "@/api/ai/aiData";

import { convertTableData } from "./components/siteTableUtil";

import PtDictDownList from "@/components/PtDictDownList/index.vue";
import siteDialog from "./components/siteDialog.vue";
import statusAiDialog from "./components/statusAiDialog.vue";
import DseOrgSelect from "@/components/dseOrgSelect";
export default {
  name: "monPointsCfg",
  components: {
    PtDictDownList,
    siteDialog,
    statusAiDialog,
    DseOrgSelect,
  },
  data() {
    let that = this;
    return {
      operaType: "add",
      editData: {},
      showDialog: false,
      searchItem: {
        name: "",
        videoServerId: "",
        cameraType: "",
        adcd: "",
        pageNum: 1,
        pageSize: 20,
      },
      adcd: [],
      orgId: [],
      loading: false,
      tableData: [],
      pageSizes: [10, 20, 30, 40, 50],
      total: 0,
      tableHeight: 200,

      tagId: "",
      serverOptions: [],
      treeData: [],
      treeProps: {
        label: "name",
        children: "childsNode",
        value: "id",
        checkStrictly: true,
      },
      defaultExpandedKeys: [],
      countObject: {
        totalQty: 0,
        sphereQty: 0,
        gunQty: 0,
        domeQty: 0,
        ptzGunQty: 0,
        speedDomeQty: 0,
        localCaptureQty: 0,
      },
      isPlugins: {
        AI: false,
      },
      showAiConf: false,
      confId: "",
      selectItem: [],
      showStatusConf: false,
    };
  },
  mounted() {
    this.initHeight();
    this.getDivisionData();
    this.getServerData();
    this.getListHandle();
    this.getPluginsData();
  },
  methods: {
    initHeight() {
      // 初始化表格高度
      this.$nextTick(() => {
        this.tableHeight = this.$refs.contentRef.clientHeight;
      });
    },
    getDivisionData() {
      let params = {
        isSync: false,
        pid: "",
      };
      QueryWholeDivisionTree4S(params).then((res) => {
        if (res.code === 200) {
          this.treeData = res.data.divisionTree;
          this.defaultExpandedKeys = this.treeData.map((d) => d.id);
        }
      });
    },
    getServerData() {
      QueryServerListRequest("").then((res) => {
        if (res.code === 200) {
          this.serverOptions = res.data;
        }
      });
    },
    getCountData() {
      CountSiteRequest(this.searchItem).then((res) => {
        if (res.code === 200) {
          this.countObject = res.data;
        } else {
          this.countObject = {
            totalQty: 0,
            sphereQty: 0,
            gunQty: 0,
            domeQty: 0,
            ptzGunQty: 0,
            speedDomeQty: 0,
            localCaptureQty: 0,
          };
        }
      });
    },
    getPluginsData() {
      pluginsCommonRequest().then((res) => {
        if (res.code === 200) {
          this.isPlugins = res.data || { AI: false };
        }
      });
    },
    changeHandle(node) {
      if (node && node.length > 0) {
        this.searchItem.adcd = node[node.length - 1];
      } else {
        this.searchItem.adcd = "";
      }
    },
    resetHandle() {
      this.searchItem = {
        name: "",
        videoServerId: "",
        cameraType: "",
        adcd: "",
        pageNum: 1,
        pageSize: 20,
      };
      this.adcd = [];
      this.orgId = [];
      this.getListHandle();
    },
    handleSearch() {
      this.searchItem.pageNum = 1;
      this.getListHandle();
    },
    handleSizeChange(val) {
      this.searchItem.pageSize = val;
      this.handleSearch();
    },
    handleCurrentChange(val) {
      this.searchItem.pageNum = val;
      this.getListHandle();
    },
    typeOptionsChange(value) {
      this.searchItem.cameraType = value;
    },
    saveSuccess() {
      this.showDialog = false;
      this.handleSearch();
    },
    closeHandle() {
      this.showDialog = false;
    },
    getListHandle() {
      this.loading = true;
      this.getCountData();
      PageSiteRequest(this.searchItem)
        .then((res) => {
          this.loading = false;
          if (res.code === 200) {
            this.total = res.data.total;
            let tableData = res.data.records;
            this.total = Number(res.data.total);
            convertTableData(tableData).then((newData) => {
              this.tableData = newData;
            });
          }
        })
        .catch((err) => {
          this.loading = false;
        });
    },
    selectChanges(node) {
      if (node && node.length > 0) {
        this.searchItem.orgId = node[node.length - 1];
      } else {
        this.searchItem.orgId = "";
      }
    },
    editHandle(row) {
      this.operaType = "edit";
      this.editData = row;
      this.showDialog = true;
    },
    changeRealTime(value, item) {
      if (item.realtimeEnable === null) {
        this.$message({
          type: "warning",
          message: "未配置算法，无法操作",
        });
        return;
      }
      realtimeConfRequest(item.id).then((res) => {
        if (res.code === 200) {
          this.$message({
            type: "success",
            message: "修改成功",
          });
          this.getListHandle();
        }
      });
    },
    changeRecordTime(value, item) {
      alarmConfRequest(item.id)
        .then((res) => {
          if (res.code === 200) {
            this.$message({
              type: "success",
              message: "修改成功",
            });
            this.getListHandle();
          } else {
            this.getListHandle();
          }
        })
        .catch(() => {
          this.getListHandle();
        });
    },
    handleSelectionChange(rows) {
      this.selectItem = rows;
    },
    deleteAllHandle() {
      if (this.selectItem.length === 0) {
        this.$message({
          type: "warning",
          message: "请至少选择一条数据",
        });
      } else {
        let ids = this.selectItem.map((d) => d.id).join(",");
        this.handelDelete(ids);
      }
    },
    handelDelete(id) {
      this.$confirm("此操作将永久删除该记录, 是否继续?", "提示", {
        confirmButtonText: "确定",
        cancelButtonText: "取消",
        type: "warning",
      }).then(() => {
        DeleteSiteRequest(id).then((res) => {
          if (res.code === 200) {
            this.$message({
              type: "success",
              message: "删除成功",
            });
            this.handleSearch();
          }
        });
      });
    },
    addHandle() {
      this.operaType = "add";
      this.showDialog = true;
    },
    confHandle(item) {
      // this.confId = item.id;
      // this.showAiConf = true;
      this.$router.push({
        path: "/monPointsCfg/aiCfg",
        query: {
          id: item.id,
          cameraIndexCode: item.cameraIndexCode,
          jrtype: item.jrtype,
          transType: item.transType,
          realtimeEnable: item.realtimeEnable,
          videoServerId: item.videoServerId,
        },
      });
    },
    closeConfHandle() {
      this.showStatusConf = false;
      this.confId = "";
    },
    confSuccessHandle() {
      this.showStatusConf = false;
      this.confId = "";
      this.handleSearch();
    },
    openStatusHandle(node) {
      this.showStatusConf = true;
      this.confId = node.id;
    },
  },
};
</script>


<style lang="scss" scoped>
.page-header-main {
  padding: 0 24px;
}
.header-btn-box {
  display: flex;
  align-items: center;
  height: 64px;
  justify-content: space-between;
}
.header-search-box {
  margin-bottom: 16px;
}

.content-box {
  width: 100%;
  height: calc(100% - 270px);
  overflow: auto;
  padding: 0 24px;
  box-sizing: border-box;
}
.pager-box {
  padding: 8px 24px;
}

.header-search-box /deep/ .el-form--inline .el-form-item {
  margin-bottom: 0;
}
.statistics-box {
  display: flex;
  align-items: center;
  height: 40px;
  background: #f0f6fc;
  margin: 0 24px 16px;
  padding: 0 16px;
  box-sizing: border-box;
}
.statistics-item {
  color: #333;
}
.statistics-num {
  color: #0d70f2;
  font-weight: bold;
}
.from-box {
  display: flex;
}
.search-btn-box {
  display: flex;
  justify-content: flex-end;
  margin-top: 16px;
}
/deep/ .el-form-item {
  flex: 1;
}
/deep/ .el-form--inline .el-form-item {
  display: flex;
  margin-right: 0;
}
/deep/ .el-form--inline .el-form-item__content {
  flex: 1;
}
/deep/ .el-form-item + .el-form-item {
  margin-left: 40px;
}
</style>