<template>
  <dse-detail-page :title="title" :showSave="false" :showCencel="false">
    <div class="detail-header">
      <div>识别时间：</div>
      <el-date-picker
        v-model="dateRange"
        type="datetimerange"
        range-separator="-"
        start-placeholder="开始日期"
        end-placeholder="结束日期"
        value-format="yyyy-MM-dd HH:mm:ss"
        @change="rangeChange"
      >
      </el-date-picker>
    </div>
    <div class="detail-box">
      <div class="canvas-box">
        <div id="echartsId" ref="chart" style="width: 100%; height: 100%" v-if="tableData.length"></div>
        <el-empty description="暂无数据" v-else></el-empty>
      </div>
      <div class="table-box" ref="tableRef">
        <el-table
          ref="table"
          v-loading="loading"
          element-loading-text="数据加载中..."
          element-loading-spinner="el-icon-loading"
          element-loading-background="rgba(0, 0, 0, 0.8)"
          border
          stripe
          tooltip-effect="light"
          row-key="id"
          :max-height="tableHeight"
          :data="tableData"
        >
          <el-table-column
            prop="createTime"
            label="识别时间"
            show-overflow-tooltip
            width="180"
            align="center"
          >
          </el-table-column>
          <el-table-column
            prop="data"
            :label="name + '(m)'"
            show-overflow-tooltip
            align="right"
          >
          </el-table-column>
          <el-table-column
            prop="alarmThreshold"
            label="识别精度"
            show-overflow-tooltip
            align="right"
          >
            <template slot-scope="scope">
              <span>{{ scope.row.alarmThreshold || "-" }}</span>
              <span>({{ getAlarmLevel(scope.row) }})</span>
            </template>
          </el-table-column>
          <el-table-column
            prop="data"
            label="操作"
            show-overflow-tooltip
            align="right"
          >
            <template slot-scope="scope">
              <span @click="previewHandle(scope.$index)" class="oper-btn">
                预览图片
              </span>
              <span @click="deleteHandle(scope.row)" class="oper-btn">
                删除
              </span>
            </template>
          </el-table-column>
        </el-table>
      </div>
    </div>
    <vue-easy-lightbox
      escDisabled
      moveDisabled
      :visible="isLightboxOpen"
      :imgs="previewList"
      :index="currentIndex"
      @hide="handleHide"
    ></vue-easy-lightbox>
  </dse-detail-page>
</template>
<script>
import dayjs from "dayjs";
import * as echarts from "echarts";

import { getAlarmListRequest, deleteSiteRecord } from "@/api/alarm/alarmData";

import DseDetailPage from "@/components/DseDetailPage/index.vue";
import { getLineData } from "./echartsLine.js";
export default {
  components: {
    DseDetailPage,
  },
  data() {
    return {
      title: "",
      searchItem: {
        adcd: "",
        orgId: "",
        algorithmCode: "",
        name: "",
        alarmType: null,
        recordType: null,
        startTime: "",
        endTime: "",
        siteId: "",
        pageNum: 1,
        pageSize: 9999,
      },
      tableData: [],
      total: 0,
      loading: false,
      tableHeight: 500,
      myChart: null,
      isLightboxOpen: false,
      currentIndex: 0,
      previewList: [],
      dateRange: [],
      name: ''
    };
  },
  beforeDestroy() {
    window.removeEventListener("resize", this.resizeEcharts);
  },
  mounted() {
    this.title = this.$route.query.title;
    this.name = this.$route.query.name;
    this.searchItem.siteId = this.$route.query.siteId;
    this.searchItem.startTime = dayjs().subtract(2, "day").format("YYYY-MM-DD HH:mm:ss");
    this.searchItem.endTime = dayjs().format("YYYY-MM-DD HH:mm:ss");
    this.dateRange = [this.searchItem.startTime, this.searchItem.endTime];
    this.initHandle();
    window.addEventListener("resize", this.resizeEcharts, false);
  },
  methods: {
    initHandle() {
      this.$nextTick(() => {
        this.tableHeight = this.$refs.tableRef.clientHeight;
        if (this.searchItem.siteId) {
          this.getListHandle();
        }
      });
    },
    initEcharts() {
      if (Array.isArray(this.tableData) && this.tableData.length > 0) {
        this.myChart = echarts.init(document.getElementById("echartsId"));
        const option = getLineData(this.tableData);
        this.myChart.setOption(option);
      }
    },
    resizeEcharts() {
      if (this.myChart) {
        this.myChart.resize();
      }
    },
    rangeChange(value) {
      if (value && value.length > 0) {
        this.searchItem.startTime = value[0];
        this.searchItem.endTime = value[1];
      } else {
        this.searchItem.startTime = "";
        this.searchItem.endTime = "";
      }
      this.getListHandle();
    },
    getListHandle() {
      this.loading = true;
      getAlarmListRequest(this.searchItem)
        .then((res) => {
          this.loading = false;
          if (res.code === 200) {
            this.total = Number(res.data.total);
            this.tableData = res.data.records;
            this.previewList = this.tableData.map((d) => d.alarmPicUrl);
            this.$nextTick(() => {
              this.initEcharts();
            })
            
          }
        })
        .catch((err) => {
          this.loading = false;
        });
    },
    getAlarmLevel(item) {
      if (item.alarmThreshold < 0.6) {
        return "差";
      } else if (item.alarmThreshold >= 0.6 && item.alarmThreshold < 0.8) {
        return "一般";
      } else if (item.alarmThreshold >= 0.8) {
        return "好";
      }
    },
    previewHandle(index) {
      this.currentIndex = index;
      this.isLightboxOpen = true;
    },
    handleHide() {
      this.isLightboxOpen = false;
    },
    deleteHandle(row) {
      this.$confirm("确定删除该条记录吗？", "提示", {
        confirmButtonText: "确定",
        cancelButtonText: "取消",
        type: "warning",
      }).then(() => {
        deleteSiteRecord(row.id).then((res) => {
          if (res.code === 200) {
            this.$message({
              message: "删除成功",
              type: "success",
            });
            this.getListHandle();
          }
        });
      });
    },
  },
};
</script>
<style lang="scss" scoped>
.detail-box {
  display: flex;
  height: calc(100% - 64px);
}
.canvas-box {
  width: 60%;
  height: 100%;
}
.table-box {
  width: 40%;
  height: 100%;
}
.detail-header {
  display: flex;
  align-items: center;
}
</style>