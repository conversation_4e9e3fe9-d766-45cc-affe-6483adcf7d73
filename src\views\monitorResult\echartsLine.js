export const getLineData = (data) => {
  let echarts = JSON.parse(JSON.stringify(data));
  echarts.reverse();
  let xData = echarts.map((d) => d.createTime);
  let seriesData = echarts.map((d) => Number(d.data));
  return {
    tooltip: {
      trigger: "axis",
    },
    xAxis: {
      type: "category",
      data: xData,
    },
    yAxis: {
      type: "value",
    },
    series: [
      {
        type: "line",
        data: seriesData,
      },
    ],
  };
};
