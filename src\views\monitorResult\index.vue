<template>
  <div class="page-wapper">
    <div class="wapper-box">
      <div class="tabs-box">
        <el-tabs v-model="searchItem.algorithmCode" @tab-click="tabClick">
          <el-tab-pane
            v-for="item in algorithmOptions"
            :key="item.code"
            :label="item.name"
            :name="item.code"
          ></el-tab-pane>
        </el-tabs>
      </div>
      <div class="header-search-box">
        <el-form :model="searchItem" ref="formRef" :inline="true">
          <el-form-item label="行政区划:">
            <el-cascader
              :props="treeProps"
              v-model="adcd"
              :options="treeData"
              style="width: 100%"
              clearable
              :show-all-levels="false"
              @change="changeHandle"
            ></el-cascader>
          </el-form-item>
          <el-form-item label="所属管理单位:">
            <dse-org-select
              @changes="selectChanges"
              :value="orgId"
            ></dse-org-select>
          </el-form-item>
          <el-form-item label="">
            <el-input
              placeholder="请输入监控点名称"
              v-model="searchItem.name"
              style="width: 300px; margin-left: 16px"
            >
              <el-button
                slot="append"
                type="primary"
                icon="el-icon-search"
                @click="handleSearch"
              ></el-button>
            </el-input>
          </el-form-item>
        </el-form>
        <el-radio-group v-model="displayMode" button-style="plain">
          <el-radio-button label="card">卡片</el-radio-button>
          <el-radio-button label="list">列表</el-radio-button>
        </el-radio-group>
      </div>

      <div class="content-box" ref="contentRef" v-loading="loading">
        <template v-if="displayMode === 'card'">
          <div class="list-item" v-for="item in tableData" :key="item.id">
            <div class="list-image-box">
              <!-- :preview-src-list="previewList" -->
              <el-image
                class="list-image"
                :preview-src-list="previewList"
                :src="item.alarmPicUrl"
              ></el-image>
              <!-- <span class="list-status" :class="['status-' + item.alarmType]">{{
              item.alarmTypeName
            }}</span> -->
            </div>

            <div class="list-item-bottom" @click="viewHandle(item)">
              <div class="bottom-left">
                <div class="list-title">
                  <div class="list-title-text">
                    {{ item.siteName || "" }}
                  </div>
                </div>
                <div class="list-dec">{{ getDecData(item) }}</div>
                <div class="list-time">{{ item.createTime }}</div>
              </div>
              <div class="btn-box">
                <!-- <el-button type="primary" plain @click="viewHandle(item)"
                  >详情</el-button
                > -->
              </div>
            </div>
          </div>
          <el-empty
            description="暂无数据"
            v-if="tableData.length === 0"
          ></el-empty>
        </template>
        <template v-else-if="displayMode === 'list'">
          <el-table
            ref="table"
            v-loading="loading"
            element-loading-text="数据加载中..."
            element-loading-spinner="el-icon-loading"
            element-loading-background="rgba(0, 0, 0, 0.8)"
            border
            stripe
            tooltip-effect="light"
            row-key="id"
            :max-height="tableHeight"
            :data="tableData"
          >
            <el-table-column label="序号" align="center" width="70">
              <template slot-scope="scope">
                <span>
                  {{
                    (searchItem.pageNum - 1) * searchItem.pageSize +
                    scope.$index +
                    1
                  }}
                </span>
              </template>
            </el-table-column>
            <el-table-column
              prop="siteName"
              label="监控点名称"
              show-overflow-tooltip
              align="left"
            >
              <template slot-scope="scope">
                <el-link type="primary" @click="viewHandle(scope.row)">{{
                  scope.row.siteName
                }}</el-link>
              </template>
            </el-table-column>
            <el-table-column
              prop="adnm"
              label="所属行政区划"
              show-overflow-tooltip
              align="left"
            >
            </el-table-column>
            <el-table-column
              prop="orgName"
              label="所属管理单位"
              show-overflow-tooltip
              align="left"
            >
            </el-table-column>
            <el-table-column
              prop="data"
              :label="activeObject.name + '(m)'"
              show-overflow-tooltip
              align="right"
            >
            </el-table-column>
            <el-table-column
              prop="createTime"
              label="识别时间"
              show-overflow-tooltip
              align="center"
            >
            </el-table-column>
            <el-table-column
              prop="alarmThreshold"
              label="识别精度"
              show-overflow-tooltip
              align="right"
            >
              <template slot-scope="scope">
                <span>{{ scope.row.alarmThreshold || "-" }}</span>
                <span>({{ getAlarmLevel(scope.row) }})</span>
              </template>
            </el-table-column>
            <el-table-column label="操作" show-overflow-tooltip align="right">
              <template slot-scope="scope">
                <span @click="previewHandle(scope.$index)" class="oper-btn">
                  预览图片
                </span>
                <!-- <span @click="viewHandle(scope.row)" class="oper-btn">
                  查看
                </span> -->
              </template>
            </el-table-column>
          </el-table>
        </template>
      </div>
      <div class="pager-box" ref="pager">
        <el-pagination
          @size-change="handleSizeChange"
          @current-change="handleCurrentChange"
          :current-page="searchItem.pageNum"
          :page-sizes="pageSizes"
          :page-size="searchItem.pageSize"
          layout="total, ->, prev, pager,next, sizes, jumper"
          :total="total"
        >
        </el-pagination>
      </div>
    </div>
    <vue-easy-lightbox
      escDisabled
      moveDisabled
      :visible="isLightboxOpen"
      :imgs="previewList"
      :index="currentIndex"
      @hide="handleHide"
    ></vue-easy-lightbox>
  </div>
</template>
<script>
import { algorithmListRequest } from "@/api/ai/aiData";
import {
  QueryWholeDivisionTree4S,
  QUERY_ORGANIZATION_TREE_4S_URL,
} from "@/api/baseInfo/baseInfoData";
import { getAlarmListRequest } from "@/api/alarm/alarmData";

import DseOrgSelect from "@/components/dseOrgSelect";



export default {
  components: {
    DseOrgSelect,
  },
  data() {
    return {
      algorithmOptions: [],
      treeProps: {
        label: "name",
        children: "childsNode",
        value: "id",
        checkStrictly: true,
      },
      treeData: [],
      defaultExpandedKeys: [],
      activeObject: {
        name: '',
        code: ''
      },
      searchItem: {
        adcd: "",
        orgId: "",
        algorithmCode: "",
        name: "",
        alarmType: null,
        recordType: "Monitor",
        startTime: "",
        endTime: "",
        pageNum: 1,
        pageSize: 20,
      },
      adcd:[],
      orgId: [],
      loading: false,
      previewList: [],
      tableData: [],
      pageSizes: [10, 20, 30, 40, 50],
      total: 0,
      displayMode: "card",
      tableHeight: 500,
      isLightboxOpen: false,
      imageUrl: [],
      currentIndex: 0,
    };
  },
  mounted() {
    this.initHandle();
    this.getDivisionData();
    this.getAlgorithData();
  },
  methods: {
    initHandle() {
      this.$nextTick(() => {
        this.tableHeight = this.$refs.contentRef.clientHeight;
      });
    },
    getAlgorithData() {
      algorithmListRequest().then((res) => {
        if (res.code === 200) {
          let data = res.data;
          this.algorithmOptions = data.filter((d) => d.type === "monitor");
          this.searchItem.algorithmCode =
            (this.algorithmOptions[0] && this.algorithmOptions[0].code) || "";
          this.activeObject = this.algorithmOptions[0];
          this.handleSearch();
        }
      });
    },
    getDivisionData() {
      let params = {
        isSync: false,
        pid: "",
      };
      QueryWholeDivisionTree4S(params).then((res) => {
        if (res.code === 200) {
          this.treeData = res.data.divisionTree;
          this.defaultExpandedKeys = this.treeData.map((d) => d.code);
        }
      });
    },
    selectChanges(node) {
      if (node && node.length > 0) {
        this.searchItem.orgId = node[node.length - 1];
      } else {
        this.searchItem.orgId = "";
      }
    },
    changeHandle(node) {
      if (node && node.length > 0) {
        this.searchItem.adcd = node[node.length - 1];
      } else {
        this.searchItem.adcd = "";
      }
    },
    tabClick() {
      this.activeObject = this.algorithmOptions.find(d => d.code === this.searchItem.algorithmCode);
      this.displayMode = "card";
      this.handleSearch();
    },
    handleSearch() {
      this.searchItem.pageNum = 1;
      this.getListHandle();
    },
    handleSizeChange(val) {
      this.searchItem.pageSize = val;
      this.handleSearch();
    },
    handleCurrentChange(val) {
      this.searchItem.pageNum = val;
      this.getListHandle();
    },
    getListHandle() {
      this.loading = true;
      getAlarmListRequest(this.searchItem)
        .then((res) => {
          this.loading = false;
          if (res.code === 200) {
            this.total = Number(res.data.total);
            this.tableData = res.data.records;
            this.previewList = this.tableData.map((d) => d.alarmPicUrl);
            this.$previewRefresh();
          }
        })
        .catch((err) => {
          this.loading = false;
        });
    },
    getDecData(item) {
      let dec = "";
      if (
        item.algorithmCode === "water_gauge" &&
        item.dataList &&
        Array.isArray(item.dataList) &&
        item.dataList.length > 0
      ) {
        dec = item.dataList
          .map((d) => {
            return "水位：" + d + "m";
          })
          .join(";");
      }
      return dec;
    },
    getAlarmLevel(item) {
      if (item.alarmThreshold < 0.6) {
        return "差";
      } else if (item.alarmThreshold >= 0.6 && item.alarmThreshold < 0.8) {
        return "一般";
      } else if (item.alarmThreshold >= 0.8) {
        return "好";
      }
    },
    previewHandle(row, index) {
      // this.imageUrl = [{ src: row.alarmPicUrl, type: "image", thumb: '', caption: '' }];
      this.currentIndex = index;
      this.isLightboxOpen = true;
    },
    handleHide() {
      this.isLightboxOpen = false;
    },
    viewHandle(row) {
      return
      this.$router.push({
        path: "/monitorResult/detail",
        query: {
          siteId: row.siteId,
          title: row.siteName,
          name: this.activeObject.name,
        },
      });
    },
  },
};
</script>
<style lang="scss" scoped>
.page-wapper {
  width: 100%;
  height: 100%;
  padding: 16px;
  box-sizing: border-box;
  background: #edf3fa;
}

.wapper-box {
  width: 100%;
  height: 100%;
  background-color: #fff;
  box-sizing: border-box;
  border-radius: 8px;
}
.header-search-box {
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 8px 16px 0;
}

.content-box {
  width: 100%;
  height: calc(100% - 176px);
  overflow: auto;
  padding: 0 24px;
  box-sizing: border-box;
}
.content-box::after {
  content: "";
  display: table;
  clear: both;
}
.pager-box {
  padding: 8px 24px;
}
.list-item {
  width: calc((100% - 64px) / 5);
  height: 284px;
  background: rgba(255, 255, 255, 0.9);
  border-radius: 4px;
  box-shadow: 0px 0px 8px 0px rgba(0, 0, 0, 0.1);
  padding: 8px;
  box-sizing: border-box;
  margin-right: 16px;
  margin-bottom: 16px;
  float: left;
  cursor: pointer;
}
.list-item:nth-child(5n) {
  margin-right: 0;
}
.list-image-box {
  width: 100%;
  height: 180px;
  position: relative;
}
.list-status {
  position: absolute;
  top: 0;
  right: 0;
  padding: 0 12px;
  height: 30px;
  line-height: 30px;
  border-bottom-left-radius: 8px;
  color: #fff;
}
.list-image {
  width: 100%;
  height: 180px;
}
.list-title {
  margin-top: 12px;
  color: #333;
}
.list-dec {
  color: #666;
  margin-top: 8px;
}
.list-time {
  color: #666;
  margin-top: 8px;
}
.status-Slight {
  background: #ffd600;
}
.status-Normal {
  background: #ff7b00;
}
.status-Critical {
  background: #eb110a;
}
.status-text-Slight {
  color: #ffd600;
}
.status-text-Normal {
  color: #ff7b00;
}
.status-text-Critical {
  color: #eb110a;
}
.list-item-bottom {
  display: flex;
  justify-content: space-between;
}
.bottom-left {
  width: calc(100% - 72px);
}
.bottom-left-yjc {
  width: 100%;
}
.btn-box {
  margin-top: 8px;
  margin-left: 5px;
}
.list-title {
  display: flex;
  align-items: center;
}
.status-text {
  margin-left: 5px;
  width: 58px;
  height: 24px;
  line-height: 24px;
  border-radius: 4px;
  background: #ff7b00;
  color: #fff;
  text-align: center;
  flex-shrink: 0;
}
.status-text-yjc {
  background: #2ed459;
}
.list-title-text {
}
</style>