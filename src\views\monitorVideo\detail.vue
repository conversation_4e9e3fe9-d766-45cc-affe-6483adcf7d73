<template>
  <div class="page-wrapper">
    <div class="wrapper-box">
      <el-tabs v-model="activeName">
        <el-tab-pane label="实时预览" name="preview"></el-tab-pane>
        <el-tab-pane label="视频回放" name="playback"></el-tab-pane>
      </el-tabs>
      <div class="back-box" @click="toBack">
        <img src="@/assets/images/icon/icon-back.png" class="icon-back" alt="" srcset="">
      </div>
      <div class="wrapper-video-box">
        <div class="real-box" v-show="activeName === 'preview'">
          <dse-video-player
            :ref="'videoRef'"
            v-if="cameraIndexCode"
            :cameraIndexCode="cameraIndexCode"
            :keyIndex="keyIndex"
            :serverType="serverType"
            :name="name"
            :transType="transType"
            :isTool="isTool"
            :viewType="viewType"
            :isClose="false"
            :realtimeEnable="realtimeEnable"
            :remark="remark"
            @close="closeHandle"
          ></dse-video-player>
        </div>
        <div class="real-box" v-show="activeName === 'playback'">
          <div>
            <record-tool
              @search="recordSearchHandle"
              layout="inline"
            ></record-tool>
          </div>
          <div class="record-box">
            <record-preview ref="recordRef"></record-preview>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>
<script>
import recordTool from "@/views/videoView/components/recordTool.vue";
import recordPreview from "@/views/videoView/components/recordPreview.vue";
export default {
  name: "monitorVideoDetail",
  components: {
    recordPreview,
    recordTool,
  },
  data() {
    return {
      activeName: "preview",
      keyIndex: 1,
      cameraIndexCode: "",
      serverType: "",
      name: "",
      transType: "0",
      isTool: true,
      viewType: "real",
      recordLocation: "",
      realtimeEnable: false,
      remark: ''
    };
  },
  mounted() {
    this.cameraIndexCode = this.$route.query.cameraIndexCode || "";
    this.serverType = this.$route.query.serverType || "";
    this.name = this.$route.query.name || "";
    this.transType = this.$route.query.transType || "0";
    this.isTool = this.$route.query.isTool === "1";
    this.recordLocation = this.$route.query.recordLocation || "";
    this.realtimeEnable = this.$route.query.realtimeEnable == "1";
    this.remark = this.$route.query.remark || ''
    if (!this.cameraIndexCode) {
      console.error("cameraIndexCode不能为空");
    }
    if (!this.serverType) {
      console.error("serverType不能为空");
    }
  },
  methods: {
    recordSearchHandle(item) {
      if (this.cameraIndexCode && this.serverType) {
        this.$refs.recordRef.recordPlay({
          jrtype: this.serverType,
          cameraIndexCode: this.cameraIndexCode,
          name: this.name,
          transType: this.transType,
          recordLocation: this.recordLocation
        }, item);
      } else {
        this.$message({
          type: "warning",
          message: "请选择摄像头",
        });
      }
    },
    closeHandle() {},
    toBack() {
      this.$router.go(-1)
    }
  },
};
</script>
<style lang="scss" scoped>
.page-wrapper {
  padding: 0;
  overflow: hidden;
}
.wrapper-box {
  position: relative;
}
.wrapper-video-box {
  height: calc(100% - 62px);
  padding: 0 8px 16px;
  box-sizing: border-box;
  border-radius: 0;
}
.real-box {
  width: 100%;
  height: 100%;
}
.record-box {
  height: calc(100% - 40px);
}
.back-box {
  position: absolute;
  right: 8px;
  top: 8px;
  width: 32px;
  height: 32px;
  border: 1px solid #d9d9d9;
  border-radius: 4px;
  display: flex;
  align-items: center;
  justify-content: center;
  cursor: pointer;
}
.icon-back {
  width: 16px;
  height: 16px;
}
</style>