<template>
  <div class="page-wrapper">
    <div class="wrapper-box">
      <div class="table-box">
        <div class="table-item" v-for="item in tableData" :key="item.id" @click="toDetail(item)">
          <div class="video-bg-box">
            <img src="@/assets/images/icon-play.png" class="video-bg" alt="" srcset="">
          </div>
          <div class="table-title" :title="item.name">{{ item.name }}</div>
        </div>
        <el-empty description="暂无数据" v-if="tableData.length === 0"></el-empty>
      </div>
      <div class="pager-box" ref="pager">
        <el-pagination
          @size-change="handleSizeChange"
          @current-change="handleCurrentChange"
          :current-page="searchItem.pageNum"
          :page-sizes="pageSizes"
          :page-size="searchItem.pageSize"
          layout="total, ->, prev, pager,next, sizes, jumper"
          :total="total"
        >
        </el-pagination>
      </div>
    </div>
  </div>
</template>
<script>
import { PageSiteRequest } from "@/api/site/siteData";
import { pluginsCommonRequest } from "@/api/common/commonData";
export default {
  name: "monitorVideo",
  data() {
    return {
      loading: false,
      tableData: [],
      total: 0,
      searchItem: {
        name: "",
        videoServerId: "",
        cameraType: "",
        adcd: "",
        dataType: '',
        dataId: '',
        pageNum: 1,
        pageSize: 20,
      },
      pageSizes: [10, 20, 30, 40, 50],
      isPlugins: {
        AI: false,
      },
    };
  },
  mounted() {
    this.searchItem.dataType = this.$route.query.dataType || "";
    this.searchItem.dataId = this.$route.query.dataId || "";
    this.getPluginsData()
    this.handleSearch()
  },
  methods: {
    getPluginsData() {
      pluginsCommonRequest().then((res) => {
        if (res.code === 200) {
          this.isPlugins = res.data || { AI: false };
        }
      });
    },
    handleSearch() {
      this.searchItem.pageNum = 1;
      this.getListHandle();
    },
    handleSizeChange(val) {
      this.searchItem.pageSize = val;
      this.handleSearch();
    },
    handleCurrentChange(val) {
      this.searchItem.pageNum = val;
      this.getListHandle();
    },
    getListHandle() {
      this.loading = true;
      PageSiteRequest(this.searchItem)
        .then((res) => {
          this.loading = false;
          if (res.code === 200) {
            this.tableData = res.data.records;
            this.total = Number(res.data.total);
          }
        })
        .catch((err) => {
          this.loading = false;
        });
    },
    toDetail(item) {
      this.$router.push({
        path: '/monitorView',
        query: {
          cameraIndexCode: item.cameraIndexCode,
          serverType: item.jrtype,
          name: item.name,
          transType: item.transType,
          isTool: 0,
          realtimeEnable: item.realtimeEnable && this.isPlugins.AI ? 1 : 0,
          recordLocation: item.recordLocation
        }
      })
    }
  },
};
</script>
<style lang="scss" scoped>
.page-wrapper {
  padding: 0;
  overflow: hidden;
}
.wrapper-box {
  box-sizing: border-box;
  border-radius: 0;
}
.table-box {
  height: calc(100% - 58px);
  overflow-y: scroll;
  padding: 16px 16px 0;
  box-sizing: border-box;
}
.table-item {
  width: calc((100% - 64px) / 5);
  height: 232px;
  background: rgba(255,255,255,0.90);
  border-radius: 4px;
  box-shadow: 0px 0px 8px 0px rgba(0,0,0,0.10); 
  padding: 8px;
  box-sizing: border-box;
  margin-right: 16px;
  margin-bottom: 16px;
  float: left;
  cursor: pointer;
}
.table-item:nth-child(5n) {
  margin-right: 0;
}
.pager-box {
  padding: 16px 24px;
}
.video-bg-box {
  background: #d4e1ee;
  width: 100%;
  height: 180px; 
  position: relative;
  background: url('@/assets/images/video-view-bg.png') no-repeat center center;
  background-size: 100% 100%;
}
.video-bg {
  width: 45px;
  height: 45px;
  position: absolute;
  top: 50%;
  left: 50%;
  transform: translate(-50%, -50%);
}
.table-title {
  height: 44px;
  line-height: 44px;
  border-radius: 8px;
  color: #333;
  text-overflow: ellipsis;
  overflow: hidden;
  white-space: nowrap;
}
</style>