<template>
  <el-dialog
    :title="title"
    :visible.sync="dialogVisible"
    width="640px"
    :before-close="handleClose"
    :close-on-click-modal="false"
  >
    <el-form
      :model="fromData"
      ref="formRef"
      :rules="rules"
      label-position="top"
    >
      <div class="form-item">
        <el-form-item label="服务器名称:" prop="name">
          <el-input
            v-model="fromData.name"
            maxlength="15"
            placeholder="请输入服务器名称"
          ></el-input>
        </el-form-item>
        <el-form-item label="服务器类型:" prop="type">
          <pt-dict-down-list
            :selectItem="fromData.type"
            :type="dictCodes.serverType"
            @change="typeOptionsChange"
             :isDefault="false"
          ></pt-dict-down-list>
        </el-form-item>
      </div>
      <div class="form-item">
        <el-form-item label="IP:" prop="ip">
          <el-input v-model="fromData.ip" placeholder="请输入IP"></el-input>
        </el-form-item>
        <el-form-item label="端口号:" prop="port">
          <el-input
            v-model="fromData.port"
            placeholder="请输入端口号"
            maxlength="8"
            @input="handlePortInput"
          ></el-input>
        </el-form-item>
      </div>
      <div class="form-item">
        <el-form-item label="key:" prop="appKey">
          <el-input
            v-model="fromData.appKey"
            placeholder="请输入key"
            maxlength="50"
            @input="handleKeyInput"
          ></el-input>
        </el-form-item>
        <el-form-item label="secret:" prop="appSecret">
          <el-input
            v-model="fromData.appSecret"
            :type="operaType === 'add' ? '' : 'password'"
            placeholder="请输入secret"
            maxlength="50"
            @input="handleSecretInput"
          ></el-input>
        </el-form-item>
      </div>
      <div class="form-item">
        <el-form-item label="账号:" prop="uname">
          <el-input
            v-model="fromData.uname"
            maxlength="15"
            placeholder="请输入账号"
            @input="handleUnameInput"
          ></el-input>
        </el-form-item>
        <el-form-item label="密码:" prop="pwd">
          <el-input
            v-model="fromData.pwd"
            :type="operaType === 'add' ? '' : 'password'"
            placeholder="请输入密码"
            maxlength="50"
            @input="handlePwdInput"
          ></el-input>
        </el-form-item>
      </div>
    </el-form>
    <div slot="footer" class="dialog-footer">
      <el-button @click="handleClose">取消</el-button>
      <el-button type="primary" @click="saveHandle" :loading="btnLoading"
        >确 定</el-button
      >
    </div>
  </el-dialog>
</template>

<script>
import PtDictDownList from "@/components/PtDictDownList/index.vue";
import {
  SaveServerRequest,
  UpdateServerRequest,
} from "@/api/server/serverData";
export default {
  name: "serverDialog",
  components: {
    PtDictDownList,
  },
  data() {
    return {
      dialogVisible: true,
      title: "新增视频服务器配置",
      fromData: {
        id: "",
        name: "",
        type: "1",
        ip: "",
        port: "",
        uname: "",
        pwd: "",
        appSecret: "",
        appKey: "",
        playType: "",
      },
      btnLoading: false,
      operaType: "add",
      rules: {
        name: [
          { required: true, message: "请输入服务器名称", trigger: "blur" },
        ],
        type: [
          { required: true, message: "请选择服务器类型", trigger: "change" },
        ],
        ip: [
          { required: true, message: "请输入ip", trigger: "blur" },
          { pattern:  /^[^\u4e00-\u9fa5]+$/, message: '服务器IP格式不正确', trigger: 'blur' }
        ],
        port: [
          { required: true, message: "请输入端口号", trigger: "blur" },
          {
            min: 2,
            max: 8,
            message: "长度在 2 到 8 个字符的整数",
            trigger: "blur",
          },
        ],
        appKey: [{ required: true, message: "请输入key", trigger: "blur" }],
        appSecret: [
          { required: true, message: "请输入secret", trigger: "blur" },
        ],
        uname: [{ required: true, message: "请输入用户名", trigger: "blur" }],
        pwd: [{ required: true, message: "请输入密码", trigger: "blur" }],
      },
    };
  },
  methods: {
    setData(data) {
      this.fromData = data;
      this.title = "修改视频服务器配置";
      this.operaType = "edit";
    },
    handleClose() {
      this.$emit("close");
    },
    typeOptionsChange(value) {
      this.fromData.type = value
    },
    handlePortInput(value) {
      this.fromData.port = value.replace(/[^\d]/g, "");
    },
    handleKeyInput(value) {
      this.fromData.appKey = value.replace(/[\u4e00-\u9fa5]/g, "");
    },
    handleSecretInput(value) {
      this.fromData.appSecret = value.replace(/[\u4e00-\u9fa5]/g, "");
    },
    handleUnameInput(value) {
      this.fromData.uname = value.replace(/[\u4e00-\u9fa5]/g, "");
    },
    handlePwdInput(value) {
      this.fromData.pwd = value.replace(/[\u4e00-\u9fa5]/g, "");
    },
    saveHandle() {
      this.$refs.formRef.validate((valid) => {
        if (valid) {
          let request = SaveServerRequest;
          if (this.fromData.id) {
            request = UpdateServerRequest;
          }
          request(this.fromData).then((res) => {
            if (res.code === 200) {
              this.$message({
                type: "success",
                message: "保存成功",
              });
              this.$emit("success");
            }
          });
        } else {
          return false;
        }
      });
    },
  },
};
</script>

<style lang="scss" scoped>
.form-item {
  display: flex;
}
.form-item /deep/ .el-form-item {
  flex: 1;
  margin-right: 48px;
}
.form-item /deep/ .el-form-item:last-child {
  margin-right: 0;
}
</style>
