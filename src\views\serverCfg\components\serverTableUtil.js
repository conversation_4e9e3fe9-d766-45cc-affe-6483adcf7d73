//
import { queryDictionary4Sel } from '@/api/common/commonData';
import dictionaryUtil from '@/utils/dictionaryUtil';
//将字典项的数字转换成中文意思
export async function convertTableData(tableData = []) {
    let newData = [];
    let tableCount = tableData.length;
    let statusOptions = await queryDictionary4Sel(dictionaryUtil.serverType, true);
    for (let i = 0; i < tableCount; i++) {
        let item = tableData[i];
        let sel = statusOptions.find(d => d.value === item.type)
        // 添加隶属关系字段
        item.typeName = sel && sel.label
        newData.push(item);
    }
    return newData;
}