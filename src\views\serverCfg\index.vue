<template>
  <div class="page-wrapper">
    <div class="wrapper-box" ref="wrapperRef">
      <div class="page-header-main">
        <div class="header-btn-box">
          <el-button type="primary" @click="addHandle" icon="el-icon-plus"
            >新增</el-button
          >
        </div>
        <div class="header-search-box">
          <el-form :model="searchItem" ref="formRef" :inline="true">
            <el-form-item label="服务器类型:">
              <pt-dict-down-list
                :selectItem="searchItem.type"
                :type="dictCodes.serverType"
                :addAll="true"
                @change="typeOptionsChange"
              ></pt-dict-down-list>
            </el-form-item>
            <el-form-item>
              <el-input
                placeholder="请输入内容"
                v-model="searchItem.name"
                style="width: 300px; margin-left: 16px"
              >
                <el-button slot="append" type="primary" icon="el-icon-search" @click="handleSearch"
                  ></el-button
                >
              </el-input>
            </el-form-item>
          </el-form>
        </div>
      </div>
      <div class="content-box" ref="contentRef">
        <el-table
          ref="table"
          v-loading="loading"
          element-loading-text="数据加载中..."
          element-loading-spinner="el-icon-loading"
          element-loading-background="rgba(0, 0, 0, 0.8)"
          border
          stripe
          tooltip-effect="light"
          row-key="id"
          :max-height="tableHeight"
          @selection-change="handleSelectionChange"
          :data="tableData"
        >
          <el-table-column label="序号" width="80" type="index">
          </el-table-column>
          <el-table-column
            prop="name"
            label="服务器名称"
            show-overflow-tooltip
            align="left"
          >
          </el-table-column>
          <el-table-column
            prop="typeName"
            label="类型"
            show-overflow-tooltip
            align="left"
          >
          </el-table-column>
          <el-table-column
            prop="platformAddress"
            label="平台地址"
            show-overflow-tooltip
            align="left"
          >
          </el-table-column>
          <el-table-column
            prop="ip"
            label="IP"
            show-overflow-tooltip
            align="left"
          >
          </el-table-column>
          <el-table-column
            prop="port"
            label="端口"
            show-overflow-tooltip
            align="left"
          >
          </el-table-column>
          <el-table-column
            prop="appKey"
            label="key"
            show-overflow-tooltip
            align="left"
          >
          </el-table-column>
          <el-table-column
            prop="appSecret"
            label="secret"
            show-overflow-tooltip
            align="left"
          >
            <template>
              <span>******</span>
            </template>
          </el-table-column>
          <el-table-column
            prop="uname"
            label="账号"
            show-overflow-tooltip
            align="left"
          >
          </el-table-column>
          <el-table-column
            prop="pwd"
            label="密码"
            show-overflow-tooltip
            align="left"
          >
            <template>
              <span>******</span>
            </template>
          </el-table-column>
          <el-table-column
            prop="enable"
            label="是否启用"
            show-overflow-tooltip
            align="left"
          >
            <template slot-scope="scope">
              <el-switch
                v-model="scope.row.enable"
                @change="($event) => changeEnable($event, scope.row)"
              ></el-switch>
            </template>
          </el-table-column>
          <el-table-column
            prop="oper"
            fixed="right"
            label="操作"
            align="right"
            width="120px"
          >
            <template slot-scope="scope">
              <span @click="editHandle(scope.row)" class="oper-btn">
                编辑
              </span>
              <span @click="handelDelete(scope.row.id)" class="oper-btn">
                删除
              </span>
            </template>
          </el-table-column>
        </el-table>
      </div>
      <div class="pager-box" ref="pager">
        <el-pagination
          @size-change="handleSizeChange"
          @current-change="handleCurrentChange"
          :current-page="searchItem.pageNum"
          :page-sizes="pageSizes"
          :page-size="searchItem.pageSize"
          layout="total, ->, prev, pager,next, sizes, jumper"
          :total="total"
        >
        </el-pagination>
      </div>
    </div>
    <server-dialog
      ref="dialogRef"
      v-if="showDialog"
      @close="closeHandle"
      @success="saveSuccessHandle"
    ></server-dialog>
  </div>
</template>
  
<script>
import { PageServerRequest, DeleteServerRequest, UpdateEnableRequest } from "@/api/server/serverData";
import { convertTableData } from './components/serverTableUtil'
import PtDictDownList from '@/components/PtDictDownList/index.vue'
import serverDialog from './components/serverDialog.vue'
export default {
  name: "serverCfg",
  components: {
    PtDictDownList,
    serverDialog
  },
  data() {
    return {
      searchItem: {
        type: "",
        name: "",
        pageNum: 1,
        pageSize: 20,
      },
      loading: false,
      tableData: [],
      pageSizes: [10, 20, 30, 40, 50],
      total: 0,
      selectLists: [],
      tableHeight: 400,

      showDialog: false,
    };
  },
  mounted() {
    this.getListHandle();
    this.initHeight();
  },
  methods: {
    initHeight() {
      // 初始化表格高度
      this.$nextTick(() => {
        this.tableHeight = this.$refs.contentRef.clientHeight;
      });
    },
    selectChanges(node) {
      this.searchItem.cateId = (node && node.cateId) || "";
    },
    handleSearch() {
      this.searchItem.pageNum = 1;
      this.getListHandle();
    },
    handleSizeChange(val) {
      this.searchItem.pageSize = val;
      this.handleSearch();
    },
    handleCurrentChange(val) {
      this.searchItem.pageNum = val;
      this.getListHandle();
    },
    getListHandle() {
      this.loading = true;
      PageServerRequest(this.searchItem)
        .then((res) => {
          this.loading = false;
          if (res.code === 200) {
            let tableData = res.data.records;
            this.total = Number(res.data.total);
            convertTableData(tableData).then(newData => {
              this.tableData = newData
            })
          }
        })
        .catch((err) => {
          this.loading = false;
        });
    },
    handleSelectionChange(rows) {
      this.selectLists = rows;
    },
    typeOptionsChange(value) {
      this.searchItem.type = value
    },
    addHandle() {
      this.showDialog = true
    },
    closeHandle() {
      this.showDialog = false
    },
    saveSuccessHandle() {
      this.closeHandle()
      this.handleSearch()
    },
    editHandle(item) {
      this.showDialog = true
      this.$nextTick(() => {
        this.$refs.dialogRef.setData(item)
      })
    },
    changeEnable(value, item) {
      UpdateEnableRequest(item.id).then((res) => {
        if (res.code === 200) {
          this.$message({
            type: "success",
            message: "修改成功",
          });
          this.getListHandle();
        }
      });
    },
    handelDelete(id) {
      this.$confirm("此操作将永久删除该记录, 是否继续?", "提示", {
        confirmButtonText: "确定",
        cancelButtonText: "取消",
        type: "warning",
      }).then(() => {
        DeleteServerRequest(id).then((res) => {
          if (res.code === 200) {
            this.$message({
              type: "success",
              message: "删除成功",
            });
            this.handleSearch();
          }
        });
      }).catch(() => {});
    },
  },
};
</script>
  
  
  <style lang="scss" scoped>
.page-header-main {
  display: flex;
  align-items: center;
  justify-content: space-between;
  height: 65px;
  padding: 0 24px;
}
.header-search-box {
  display: flex;
  align-items: center;
}

.content-box {
  width: 100%;
  height: calc(100% - 120px);
  overflow: auto;
  padding: 0 24px;
  box-sizing: border-box;
}
.pager-box {
  padding: 8px 24px;
}

.header-search-box /deep/ .el-form--inline .el-form-item {
  margin-bottom: 0;
}
/deep/ .el-form--inline .el-form-item:last-child {
  margin-right: 0;
}
</style>