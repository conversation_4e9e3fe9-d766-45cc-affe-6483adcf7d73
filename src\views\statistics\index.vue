<template>
  <div class="page-wrapper">
    <div class="count-wrapper" ref="count">
      <img :src="require('@/assets/images/icon_jk.png')" class="icon_jk" />
      <span class="label">监控点总数</span>
      <span class="value">{{ countData.allNum || 0 }}</span>
      <span class="icon green"></span>
      <span class="label">在线监控</span>
      <span class="value">{{ countData.onLineNum || 0 }}</span>
      <span class="icon red"></span>
      <span class="label">离线监控</span>
      <span class="value">{{ countData.offLineNum || 0 }}</span>
    </div>
    <div class="statistics-box">
      <div class="page-header-main">
        <div class="page-header-left">
          <el-button type="primary" icon="el-icon-refresh" @click="refreshHandle" :loading="btnLoading">刷新状态</el-button>
        </div>
        <div class="header-search-box">
          <el-form :model="searchItem" ref="formRef" :inline="true">
            
            <el-form-item label="管理单位:">
                <dse-org-select
                  @changes="selectChanges"
                  :value="orgId"
                ></dse-org-select>
              </el-form-item>
            <el-form-item label="在线状态:">
              <el-select v-model="searchItem.inline" placeholder="请选择" clearable>
                <el-option
                  v-for="item in statusOptions"
                  :key="item.value"
                  :label="item.label"
                  :value="item.value"
                >
                </el-option>
              </el-select>
            </el-form-item>
            <el-form-item>
              <el-input
                placeholder="请输入内容"
                v-model="searchItem.siteName"
                style="width: 300px; margin-left: 16px"
              >
                <el-button slot="append" type="primary" @click="handleSearch"
                  >搜索</el-button
                >
              </el-input>
            </el-form-item>
          </el-form>
        </div>
      </div>
      <div class="content-box" ref="contentRef">
        <el-table
          ref="table"
          v-loading="loading"
          element-loading-text="数据加载中..."
          element-loading-spinner="el-icon-loading"
          element-loading-background="rgba(0, 0, 0, 0.8)"
          border
          stripe
          tooltip-effect="light"
          row-key="id"
          :max-height="tableHeight"
          :data="tableData"
        >
        <el-table-column align="center" label="序号" width="80">
            <template slot-scope="scope">
              <span>{{ (searchItem.pageNum - 1) * searchItem.pageSize + scope.$index + 1 }}</span>
            </template>
          </el-table-column>
          <el-table-column
            prop="siteName"
            label="监控点名称"
            show-overflow-tooltip
            header-align="center"
            align="center"
          >
          </el-table-column>
          <el-table-column
            prop="serverName"
            label="视频服务器"
            show-overflow-tooltip
            header-align="center"
            align="center"
          >
          </el-table-column>
          <el-table-column
            prop="orgName"
            label="管理单位"
            show-overflow-tooltip
            header-align="center"
            align="center"
          >
          </el-table-column>
          <el-table-column
            prop="adcdName"
            label="行政区划"
            show-overflow-tooltip
            header-align="center"
            align="center"
          >
          </el-table-column>
          <el-table-column
            prop="address"
            label="安装位置"
            show-overflow-tooltip
            header-align="center"
            align="center"
          >
          </el-table-column>
          <el-table-column
            prop="cameraTypeName"
            label="视频监控点类型"
            show-overflow-tooltip
            header-align="center"
            align="center"
          >
          </el-table-column>
          <el-table-column
            prop="syncTime"
            label="上次刷新时间"
            show-overflow-tooltip
            header-align="center"
            align="center"
          >
          </el-table-column>
          <el-table-column
            prop="inlineName"
            label="在线情况"
            show-overflow-tooltip
            header-align="center"
            align="center"
          >
            <template slot-scope="scope">
              <div class="inline-box">
                <span class="icon" :class="[
                  scope.row.inline === '1' ? 'green' : 'red'
                ]"></span>
                <span style="margin-left: 8px;">{{ scope.row.inlineName }}</span>
              </div>
            </template>
          </el-table-column>
        </el-table>
      </div>
      
      <div class="pager-box" ref="pager">
        <el-pagination
          @size-change="handleSizeChange"
          @current-change="handleCurrentChange"
          :current-page="searchItem.pageNum"
          :page-sizes="pageSizes"
          :page-size="searchItem.pageSize"
          layout="total, ->, prev, pager,next, sizes, jumper"
          :total="total"
        >
        </el-pagination>
      </div>
      </div>
    </div>
  </div>
</template>

<script>
import dayjs from "dayjs";
import { ListStatsRequest, CountStatsRequest } from "@/api/stats/statsData";
import { refreshOnlineStatusRequest } from "@/api/play/playData";

import DseOrgSelect from "@/components/dseOrgSelect";
export default {
  name: "statisticsPage",
  components: {
    DseOrgSelect,
  },
  data() {
    return {
      countData: {},
      orgId: [],
      searchItem: {
        orgId: "",
        siteName: "",
        inline: "",
        statsTime: "",
        pageNum: 1,
        pageSize: 20,
      },
      tableHeight: 400,
      pageSizes: [10, 20, 30, 40, 50],
      total: 0,
      tableData: [],
      statusOptions: [
        {
          label: "在线",
          value: "1",
        },
        {
          label: "离线",
          value: "0",
        },
      ], //在线状态
      loading: false,
      btnLoading: false,
      syncTime: "",
    };
  },
  mounted() {
    this.initHeight();
    this.getListHandle();
  },
  methods: {
    initHeight() {
      // 初始化表格高度
      this.$nextTick(() => {
        this.tableHeight = this.$refs.contentRef.clientHeight;
      });
    },
    handleSearch() {
      this.searchItem.pageNum = 1;
      this.getListHandle();
    },
    handleSizeChange(val) {
      this.searchItem.pageSize = val;
      this.handleSearch();
    },
    handleCurrentChange(val) {
      this.searchItem.pageNum = val;
      this.getListHandle();
    },
    getCountHandle() {
      CountStatsRequest(this.searchItem).then((res) => {
        if (res.code === 200) {
          this.countData = res.data;
        }
      });
    },
    getListHandle() {
      this.loading = true;
      this.getCountHandle();
      ListStatsRequest(this.searchItem)
        .then((res) => {
          this.loading = false;
          if (res.code === 200) {
            this.tableData = res.data.records;
            this.total = Number(res.data.total);
            this.tableData = this.tableData.map((d) => {
              return {
                ...d,
                syncTime: d.syncTime
                  ? dayjs(d.syncTime).format("YYYY-MM-DD HH:mm:ss")
                  : "",
              };
            });
          }
        })
        .catch(() => {
          this.loading = false;
        });
    },
    refreshHandle() {
      this.btnLoading = true;
      refreshOnlineStatusRequest()
        .then((res) => {
          this.btnLoading = false;
          if (res.code === 200) {
            this.$message({
              type: "success",
              message: "刷新成功",
            });
            this.handleSearch();
            this.getCountHandle();
          }
        })
        .catch(() => {
          this.btnLoading = false;
        });
    },
    selectChanges(node) {
      if (node && node.length > 0) {
        this.searchItem.orgId = node[node.length - 1];
      } else {
        this.searchItem.orgId = "";
      }
    },
  },
};
</script>

<style lang="scss" scoped>
.count-wrapper {
  display: flex;
  align-items: center;
  background: #fff;
  height: 64px;
  padding: 0 24px;
  box-sizing: border-box;
  border-radius: 4px;
  margin-bottom: 16px;
  .icon_jk {
    width: 40px;
    height: 40px;
  }
  .label {
    color: #666666;
    font-size: 14px;
    margin-left: 8px;
  }
  .value {
    color: #333333;
    font-weight: 700;
    font-size: 24px;
    margin-left: 16px;
  }
}

.icon {
  margin-left: 50px;
  width: 8px;
  height: 8px;
  border-radius: 50%;
  display: inline-block;
}
.green {
  background: #46a805;
}
.red {
  background: #999999;
}
.inline-box .icon {
  margin-left: 0;
}
.statistics-box {
  height: calc(100% - 80px);
  background: #fff;
  border-radius: 4px;
}

.page-header-main {
  display: flex;
  align-items: center;
  justify-content: space-between;
  height: 65px;
  padding: 0 24px;
}

.content-box {
  width: 100%;
  height: calc(100% - 120px);
  overflow: auto;
  padding: 0 24px;
  box-sizing: border-box;
}
.pager-box {
  padding: 8px 24px;
}
.header-search-box /deep/ .el-form--inline .el-form-item {
  margin-bottom: 0;
}
/deep/ .el-form--inline .el-form-item:last-child {
  margin-right: 0;
}
.page-header-left {
  display: flex;
  align-items: center;
}
.leftLabel {
  color: #666666;
}
.leftValue {
  color: #333;
}
</style>