<template>
  <el-dialog
    :title="title"
    :visible.sync="dialogVisible"
    :close-on-click-modal="false"
    width="640px"
    :before-close="handleClose"
  >
    <el-form
      :model="formData"
      ref="formRef"
      :rules="rules"
      label-position="top"
    >
      <el-row>
        <el-col :span="11">
          <el-form-item label="目录名称:" prop="name">
            <el-input
              v-model="formData.name"
              maxlength="15"
              placeholder="请输入目录名称"
            ></el-input>
          </el-form-item>
        </el-col>
        <el-col :span="11" :offset="2">
          <el-form-item label="上级目录" :prop="formData.pid === '0' ? '' : 'pid'">
            <el-tree-select
              ref="treeRef"
              v-model="formData.pid"
              :default-expanded-keys="defaultExpandedKeys"
              :defaultValue="formData.pName"
              :data="catalogOptions"
              node-key="id"
              :props="treeProps"
              @change="changeHandle"
            >
              <template v-slot:default="scope">
                <span class="node-label">{{ scope.node.label }}</span>
              </template>
            </el-tree-select>
          </el-form-item>
        </el-col>
      </el-row>
      <el-row>
        <!-- <el-col :span="11">
          <el-form-item label="排序">
            <el-input-number
              v-model="formData.sortNo"
              :min="0"
              :precision="0"
              :step="1"
              :max="9999"
              label="顺序"
              style="width: 100%"
            ></el-input-number>
          </el-form-item>
        </el-col> -->
        <el-col :span="11">
          <el-form-item label="所属标签" prop="tagId">
            <el-select
              style="width: 100%"
              v-model="formData.videoTagsId"
              placeholder="请选择"
              disabled
            >
              <el-option
                v-for="item in tagData"
                :key="item.id"
                :label="item.name"
                :value="item.id"
              >
              </el-option>
            </el-select>
          </el-form-item>
        </el-col>
      </el-row>
      <el-row>
        <el-col :span="23">
          <el-form-item label="">
            <tree-transfer
              ref="t1"
              :title="['待选视频', '已选视频']"
              :from_data="notSelectSiteList"
              :to_data="selectedSiteList"
              :defaultProps="{ label: 'name', children: 'siteList' }"
              rootPidValue="-1"
              node_key="id"
              pid="pid"
              filter
              :filterNode="filterMethod"
              openAll
            >
            </tree-transfer>
          </el-form-item>
        </el-col>
      </el-row>
    </el-form>
    <span slot="footer" class="dialog-footer">
      <el-button @click="handleClose">取 消</el-button>
      <el-button type="primary" @click="saveHandle" :loading="btnLoading"
        >确 定</el-button
      >
    </span>
  </el-dialog>
</template>

<script>
import treeTransfer from "el-tree-transfer";
// import treeTransfer from '@/components/tree-transfer/transfer-extend.vue'
import {
  queryDirTreeRequest,
  getNotSelectSiteRequest,
  updateDirRequest,
  addChildDirRequest
} from "@/api/dir/dirData";
import { ListTagRequest } from "@/api/tag/tagData";

export default {
  name: "PtCatalogChildDialog",
  components: { treeTransfer },
  props: {
    tagId: {
      type: String,
      default: "",
    },
    itemData: {
      type: Object,
      default() {
        return {};
      },
    },
    operaType: {
      type: String,
      default: "",
    },
  },
  watch: {
    itemData: {
      handler() {
        this.initHandle();
      },
      immediate: true,
      deep: true,
    },
  },
  data() {
    return {
      dialogVisible: true,
      formData: {
        id: "",
        name: "",
        pid: "",
        pName: "",
        sortNo: 1,
        videoTagsId: "",
        siteList: [],
      },
      rules: {
        name: [{ required: true, message: "请输入目录名称", trigger: "blur" }],
        pid: [{ required: true, message: "请选择上级目录", trigger: "blur" }],
      },
      title: "新增目录",
      catalogOptions: [],
      defaultExpandedKeys: [],
      treeProps: {
        label: "name",
        children: "children",
      },
      tagData: [],
      notSelectSiteList: [],
      selectedSiteList: [],
      btnLoading: false,
      filterMethod(query, item) {
        return item.name.indexOf(query) > -1;
      },
    };
  },
  mounted() {},
  methods: {
    initHandle() {
      if (this.operaType === "add") {
        this.title = '新增目录'
        this.formData.id = "";
        this.formData.name = "";
        this.formData.pid = this.itemData.pid || "";
        this.formData.pName = this.itemData.pName || "";
        this.formData.videoTagsId = this.itemData.tagId || "";
      } else {
        this.title = '修改目录'
        this.formData.id = this.itemData.id || "";
        this.formData.name = this.itemData.name || "";
        this.formData.pid = this.itemData.pid || "";
        this.formData.pName = this.itemData.pName || "";
        this.formData.sortNo = this.itemData.sortNo || 1;
        this.formData.siteList = this.itemData.siteList || [];
        this.formData.videoTagsId = this.itemData.tagId || "";
      }
      this.getCatalogData();
      this.getTagDataHandle();
      this.getNotSelectData();
    },
    getNotSelectData() {
      let params = {
        tagId: this.formData.videoTagsId,
        dirId: this.formData.id,
      };
      getNotSelectSiteRequest(params).then((res) => {
        if (res.code === 200) {
          let selectedList = res.data.selectedList || [];
          let notSelectList = res.data.notSelectList || [];
          selectedList.forEach((data) => {
            data.pid = "-1";
          });
          this.initSiteData(notSelectList, "-1");
          this.selectedSiteList = selectedList;
          this.notSelectSiteList = notSelectList;
        }
      });
    },
    //初始化待选数据
    initSiteData(data, pid) {
      if (Array.isArray(data)) {
        for (let item of data) {
          item.pid = pid;
          if (item.siteList) {
            this.initSiteData(item.siteList, item.id);
          }
        }
      }
    },
    getTagDataHandle() {
      ListTagRequest().then((res) => {
        if (res.code === 200) {
          this.tagData = res.data;
        }
      });
    },
    getCatalogData() {
      queryDirTreeRequest(this.tagId).then((res) => {
        if (res.code === 200) {
          this.catalogOptions = res.data;
        }
      });
    },
    handleClose() {
      this.$emit("close");
    },
    //格式化已选数据
    initSelectedSiteData(arr, data) {
      if (Array.isArray(data)) {
        for (let item of data) {
          if (item.siteList) {
            this.initSelectedSiteData(arr, item.siteList);
          } else {
            arr.push(item.id);
          }
        }
      }
    },
    saveHandle() {
      this.$refs.formRef.validate(vail => {
        if(vail) {
          this.btnLoading = true
          let request = addChildDirRequest
          if(this.operaType === 'edit') {
            request = updateDirRequest
          }
          let siteList = []
          this.initSelectedSiteData(siteList, this.selectedSiteList);
          this.formData.siteList = siteList
          request(this.formData).then(res => {
            this.btnLoading = false
            if(res.code === 200) {
              this.$message({
                type: 'success',
                message: '保存成功'
              })
              this.$emit('save-success')
            }
          }).catch(() => {
            this.btnLoading = false
          })
        }
      })
    },
    changeHandle() {},
  },
};
</script>


<style lang="scss" scoped>
/deep/ .el-button.is-circle {
  border-radius: 4px;
}
</style>