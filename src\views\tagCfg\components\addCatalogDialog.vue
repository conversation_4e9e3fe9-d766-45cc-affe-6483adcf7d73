<template>
  <el-dialog
    :title="title"
    :visible.sync="dialogVisible"
    :close-on-click-modal="false"
    width="480px"
    :before-close="handleClose"
  >
    <el-form
      :model="formData"
      ref="formRef"
      :rules="rules"
      label-position="top"
    >
      <el-form-item label="目录名称:" prop="name">
        <el-input
          v-model="formData.name"
          maxlength="15"
          placeholder="请输入目录名称"
        ></el-input>
      </el-form-item>

      <el-form-item label="顺序:" prop="sortNo">
        <el-input-number
          v-model="formData.sortNo"
          :min="0"
          :precision="0"
          :step="1"
          :max="9999"
          label="顺序"
          style="width: 100%"
        ></el-input-number>
      </el-form-item>
    </el-form>
    <span slot="footer" class="dialog-footer">
      <el-button @click="handleClose">取 消</el-button>
      <el-button type="primary" @click="saveHandle" :loading="btnLoading"
        >确 定</el-button
      >
    </span>
  </el-dialog>
</template>

<script>
import { addParentDirRequest, updateDirRequest } from '@/api/dir/dirData'
export default {
  name: "addCatalogDialog",
  props: {
    itemData: {
      type: Object,
      default() {
        return {}
      }
    },
    operaType: {
      type: String,
      default: 'add'
    }
  },
  data() {
    return {
      title: "新增目录",
      dialogVisible: true,
      formData: {
        name: '',
        sortNo: 1
      },
      rules: {
        name: [
          { required: true, message: "请输入目录名称", trigger: "blur" },
        ],
        sortNo: [
          { required: true, message: "请输入顺序", trigger: "blur" },
        ]
      },
      btnLoading: false
    };
  },
  mounted() {
    if(this.operaType === 'add') {
      this.title = '新增目录'
      this.formData.videoTagsId = this.itemData.videoTagsId || ''
    } else {
      this.title = '修改目录'
      this.formData.id = this.itemData.id
      this.formData.name = this.itemData.name
      this.formData.pid = this.itemData.pid
      this.formData.sortNo = this.itemData.sortNo
      this.formData.siteList = []
    }
    
  },
  methods: {
    handleClose() {
      this.$emit("close");
    },
    saveHandle() {
      this.$refs.formRef.validate(vail => {
        if(vail) {
          this.btnLoading = true
          let request = addParentDirRequest;
          if(this.operaType === 'edit') {
            request = updateDirRequest
          }
          request(this.formData).then(res => {
            if(res.code === 200) {
              this.$message({
                type: 'success',
                message: '保存成功'
              })
              this.$emit('save-success')
            }
          })
        }
      })
    }
  },
};
</script>

<style>
</style>