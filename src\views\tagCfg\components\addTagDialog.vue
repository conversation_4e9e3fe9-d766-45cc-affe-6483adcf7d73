<template>
  <el-dialog
    :title="title"
    :visible.sync="dialogVisible"
    :close-on-click-modal="false"
    width="480px"
    :before-close="handleClose"
  >
    <el-form
      :model="formData"
      ref="formRef"
      :rules="rules"
      label-position="top"
    >
      <el-form-item label="标签名称:" prop="name">
        <el-input
          v-model="formData.name"
          maxlength="15"
          placeholder="请输入标签名称"
        ></el-input>
      </el-form-item>

      <el-form-item label="顺序:" prop="sortNo">
        <el-input-number
          v-model="formData.sortNo"
          :min="0"
          :precision="0"
          :step="1"
          :max="9999"
          label="顺序"
          style="width: 100%"
        ></el-input-number>
      </el-form-item>
    </el-form>
    <span slot="footer" class="dialog-footer">
      <el-button @click="handleClose">取 消</el-button>
      <el-button type="primary" @click="saveHandle" :loading="btnLoading"
        >确 定</el-button
      >
    </span>
  </el-dialog>
</template>

<script>
import { saveTagRequest, updateTagRequest } from '@/api/tag/tagData'
export default {
  name: "addTagDialog",
  props: {
    itemData: {
      type: Object,
      default() {
        return {}
      }
    }
  },
  data() {
    return {
      title: "新增标签",
      dialogVisible: true,
      formData: {
        id: '',
        name: '',
        sortNo: 1
      },
      btnLoading: false,
      rules: {
        name: [
          { required: true, message: "请输入标签名称", trigger: "blur" },
        ],
        sortNo: [
          { required: true, message: "请输入顺序", trigger: "blur" },
        ]
      }
    };
  },
  mounted() {
    this.formData.id = this.itemData.id || '';
    if(this.formData.id) {
      this.formData.name = this.itemData.name || '';
      this.formData.sortNo = this.itemData.sortNo;
    }
  },
  methods: {
    handleClose() {
      this.$emit("close");
    },
    saveHandle() {
      this.$refs.formRef.validate(vail => {
        if(vail) {
          this.btnLoading = true
          let request = saveTagRequest;
          if(this.formData.id) {
            request = updateTagRequest
          }
          request(this.formData).then(res => {
            if(res.code === 200) {
              this.$message({
                type: 'success',
                message: '保存成功'
              })
              this.$emit('save-success')
            }
          })
        }
      })
    }
  },
};
</script>

<style>
</style>