<template>
  <div class="page-wrapper">
    <div class="tab-wrapper" ref="tag">
      <div
        class="tab-item"
        :class="{ 'tab-item-active': tag.id === activeTag }"
        v-for="(tag, index) in tagData"
        :key="index"
        @click="changeTag(tag)"
      >
        <span>{{ tag.name }}</span>
        <!-- <i class="el-icon-close" @click.stop="delTag(tag)"></i> -->
        <el-dropdown
          :hide-on-click="false"
          @command="($event) => handleCommand($event, tag)"
        >
          <span class="el-dropdown-link">
            <img
              src="@/assets/images/icon/icon-more.png"
              class="icon-more"
              alt=""
              srcset=""
            />
          </span>
          <el-dropdown-menu slot="dropdown">
            <el-dropdown-item command="edit">编辑</el-dropdown-item>
            <el-dropdown-item command="delete">删除</el-dropdown-item>
          </el-dropdown-menu>
        </el-dropdown>
      </div>
      <div class="tab-item tab-add" @click="addTag">
        <i class="el-icon-plus"></i>
      </div>
    </div>
    <div class="wrapper page-wrapper-lf">
      <div class="page-wrapper-lf-left" v-loading="catalogTreeLoading">
        <div class="btn-box">
          <el-button style="width: 100%" @click="addOrUpdateCatalog('add', {})"
            >新增一级目录</el-button
          >
        </div>

        <div class="tree-box">
          <el-tree
            ref="tree"
            class="custom-tree"
            :data="catalogTree"
            node-key="id"
            default-expand-all
            highlight-current
            :expand-on-click-node="false"
            :draggable="true"
            :allow-drag="allowDrag"
            :allow-drop="allowDrop"
            @node-drop="handleDrop"
            @node-click="clickTreeNode"
          >
            <div class="custom-tree-node" slot-scope="{ node, data }">
              <span class="custom-tree-node-label text-limit1">{{
                data.name
              }}</span>
              <span>
                <img
                  :src="require('@/assets/images/icon/icon_folder_add.png')"
                  @click.stop="addOrUpdateCatalogChild('add', data, node)"
                />
                <img
                  :src="require('@/assets/images/icon/icon_edit.png')"
                  @click.stop="addOrUpdateCatalogChild('edit', data, node)"
                />
                <img
                  :src="require('@/assets/images/icon/icon_del.png')"
                  @click.stop="delCatalog(data)"
                />
              </span>
            </div>
          </el-tree>
        </div>
      </div>
      <div class="page-wrapper-lf-right">
        <div class="search-wrapper" ref="search">
          <el-button
            :disabled="multipleSelection.length <= 0"
            @click="batchUnLink"
            >批量解除关联</el-button
          >
          <div class="header-search-box">
            <el-form :model="searchItem" ref="formRef" :inline="true">
              <el-form-item label="监控点类型:">
                <pt-dict-down-list
                  :selectItem="searchItem.cameraType"
                  :type="dictCodes.cameraType"
                  :addAll="true"
                  @change="typeOptionsChange"
                ></pt-dict-down-list>
              </el-form-item>
              <el-form-item label="行政区划:">
                <el-cascader
                  :props="treeProps"
                  v-model="adcd"
                  :options="treeData"
                  style="width: 100%"
                  @change="changeHandle"
                  :show-all-levels="false"
                  clearable
                ></el-cascader>
              </el-form-item>
              <el-form-item>
                <el-input
                  placeholder="请输入监控点名称"
                  v-model="searchItem.siteName"
                  style="width: 250px; margin-left: 16px"
                >
                  <el-button
                    slot="append"
                    type="primary"
                    icon="el-icon-search"
                    @click="handleSearch"
                  ></el-button>
                </el-input>
              </el-form-item>
            </el-form>
          </div>
        </div>
        <div class="statistics-box">
          <div class="statistics-item">
            监控站总数<span class="statistics-num">{{
              countObject.totalQty
            }}</span
            >个，
          </div>
          <div class="statistics-item">
            球机<span class="statistics-num">{{ countObject.sphereQty }}</span
            >个，
          </div>
          <div class="statistics-item">
            枪机<span class="statistics-num">{{ countObject.gunQty }}</span
            >个，
          </div>
          <div class="statistics-item">
            半球<span class="statistics-num">{{ countObject.domeQty }}</span
            >个，
          </div>
          <div class="statistics-item">
            云台枪机<span class="statistics-num">{{
              countObject.ptzGunQty
            }}</span
            >个，
          </div>
          <div class="statistics-item">
            快球<span class="statistics-num">{{
              countObject.speedDomeQty
            }}</span
            >个，
          </div>
          <div class="statistics-item">
            本地采集输入<span class="statistics-num">{{
              countObject.localCaptureQty
            }}</span
            >个
          </div>
        </div>
        <div class="content-box" ref="contentRef">
          <el-table
            ref="table"
            v-loading="loading"
            element-loading-text="数据加载中..."
            element-loading-spinner="el-icon-loading"
            element-loading-background="rgba(0, 0, 0, 0.8)"
            border
            stripe
            tooltip-effect="light"
            row-key="id"
            :max-height="tableHeight"
            :data="tableData"
            @selection-change="handleSelectionChange"
          >
            <el-table-column type="selection" width="55"> </el-table-column>
            <el-table-column label="序号" width="80" type="index">
            </el-table-column>
            <el-table-column
              prop="dirName"
              label="父级目录"
              show-overflow-tooltip
              align="left"
            >
            </el-table-column>
            <el-table-column
              prop="siteName"
              label="监控点名称"
              show-overflow-tooltip
              align="left"
            >
            </el-table-column>
            <el-table-column
              prop="serverName"
              label="视频服务器"
              show-overflow-tooltip
              align="left"
            >
            </el-table-column>
            <el-table-column
              prop="adcdName"
              label="行政区划"
              show-overflow-tooltip
              align="left"
            >
            </el-table-column>
            <el-table-column
              prop="cameraTypeName"
              label="监控点类型"
              show-overflow-tooltip
              align="left"
            >
            </el-table-column>
            <el-table-column
              prop="oper"
              fixed="right"
              label="操作"
              align="right"
              width="120px"
            >
              <template slot-scope="scope">
                <span @click="handelDelete(scope.row.id)" class="oper-btn">
                  取消关联
                </span>
              </template>
            </el-table-column>
          </el-table>
        </div>
        <div class="pager-box" ref="pager">
          <el-pagination
            @size-change="handleSizeChange"
            @current-change="handleCurrentChange"
            :current-page="searchItem.pageNum"
            :page-sizes="pageSizes"
            :page-size="searchItem.pageSize"
            layout="total, ->, prev, pager,next, sizes, jumper"
            :total="total"
          >
          </el-pagination>
        </div>
      </div>
    </div>
    <add-tag-dialog
      v-if="showAddTag"
      :itemData="editTagObject"
      @close="closeTagHandle"
      @save-success="saveTagSuccessHandle"
    ></add-tag-dialog>
    <add-catalog-dialog
      v-if="showAddCatalog"
      :itemData="editCatalogData"
      :operaType="operaCatalogType"
      @close="closeCatalogHandle"
      @save-success="saveCatalogSuccessHandle"
    ></add-catalog-dialog>
    <pt-catalog-child-dialog
      v-if="showCatalogDialog"
      :tagId="activeTag"
      :operaType="operaType"
      :itemData="catalogEditObject"
      @close="closeChildHandle"
      @save-success="saveChildSuccess"
    ></pt-catalog-child-dialog>
  </div>
</template>

<script>
import { ListTagRequest, deleteTagRequest } from "@/api/tag/tagData";
import {
  queryDirTreeRequest,
  deleteDirRequest,
  sortDirRequest,
} from "@/api/dir/dirData";
import { QueryWholeDivisionTree4S } from "@/api/baseInfo/baseInfoData";
import {
  getVideoTagList,
  getVideoSiteCount,
  unLinkRelRequest,
  batchUnLinkRequest,
} from "@/api/rel/relData";

import { convertTableData } from "./../monPointsCfg/components/siteTableUtil";

import PtDictDownList from "@/components/PtDictDownList/index.vue";
import addTagDialog from "./components/addTagDialog.vue";
import addCatalogDialog from "./components/addCatalogDialog.vue";
import PtCatalogChildDialog from "./components/PtCatalogChildDialog.vue";
export default {
  name: "tagCfg",
  components: {
    PtDictDownList,
    addTagDialog,
    addCatalogDialog,
    PtCatalogChildDialog,
  },
  data() {
    return {
      tagData: [],
      activeTag: "",
      actvieCatalog: "",
      catalogTreeLoading: false,

      catalogTree: [],
      multipleSelection: [],

      searchItem: {
        siteName: "",
        cameraType: "",
        adcd: "",
        tagId: "",
        dirId: "",
        pageNum: 1,
        pageSize: 20,
      },
      adcd: [],
      tableData: [],
      pageSizes: [10, 20, 30, 40, 50],
      total: 0,
      tableHeight: 200,
      loading: false,
      defaultExpandedKeys: [],
      countObject: {
        totalQty: 0,
        sphereQty: 0,
        gunQty: 0,
        domeQty: 0,
        ptzGunQty: 0,
        speedDomeQty: 0,
        localCaptureQty: 0,
      },
      treeData: [],
      treeProps: {
        label: "name",
        children: "childsNode",
        value: "id",
        checkStrictly: true,
      },

      showAddTag: false,
      showAddCatalog: false,
      editCatalogData: {},
      operaCatalogType: "add",
      showCatalogDialog: false,
      operaType: "add",
      catalogEditObject: {},
      editTagObject: {},
    };
  },
  mounted() {
    this.initHeight();
    this.getDivisionData();
    this.getTagDataHandle();
  },
  methods: {
    initHeight() {
      // 初始化表格高度
      this.$nextTick(() => {
        this.tableHeight = this.$refs.contentRef.clientHeight;
      });
    },
    getDivisionData() {
      let params = {
        isSync: false,
        pid: "",
      };
      QueryWholeDivisionTree4S(params).then((res) => {
        if (res.code === 200) {
          this.treeData = res.data.divisionTree;
          this.defaultExpandedKeys = this.treeData.map((d) => d.id);
        }
      });
    },
    getTagDataHandle() {
      ListTagRequest().then((res) => {
        if (res.code === 200) {
          this.tagData = res.data;
          if (this.tagData.length > 0) {
            this.activeTag = this.activeTag
              ? this.activeTag
              : this.tagData[0].id;
            this.getCatalogTree();
          }
        }
      });
    },
    getCatalogTree() {
      this.catalogTreeLoading = true;
      queryDirTreeRequest(this.activeTag)
        .then((res) => {
          this.catalogTreeLoading = false;
          if (res.code === 200) {
            this.catalogTree = res.data;
            if (this.catalogTree.length > 0) {
              this.actvieCatalog = this.actvieCatalog
                ? this.actvieCatalog
                : this.catalogTree[0].id;
              this.$nextTick(() => {
                this.$refs.tree &&
                  this.$refs.tree.setCurrentKey(this.actvieCatalog);
              });
              this.handleSearch();
            }
          }
        })
        .catch(() => {
          this.catalogTreeLoading = false;
        });
    },
    changeTag(tag) {
      this.activeTag = tag.id;
      this.tableData = [];
      this.total = 0;
      this.actvieCatalog = "";
      this.searchItem.siteName = "";
      this.searchItem.pageNum = 1;
      this.countObject = {
        totalQty: 0,
        sphereQty: 0,
        gunQty: 0,
        domeQty: 0,
        ptzGunQty: 0,
        speedDomeQty: 0,
        localCaptureQty: 0,
      };
      this.getCatalogTree();
    },
    addTag() {
      this.editTagObject.id = "";
      this.editTagObject.name = "";
      this.showAddTag = true;
    },
    handleCommand(command, item) {
      if (command === "delete") {
        this.delTag(item);
      } else if (command === "edit") {
        this.editTagObject.id = item.id;
        this.editTagObject.name = item.name;
        this.editTagObject.sortNo = item.sortNo;
        this.showAddTag = true;
      }
    },
    delTag(tag) {
      this.$confirm("确定删除此数据?", "提示", {
        dangerouslyUseHTMLString: true,
        confirmButtonText: "确定",
        cancelButtonText: "取消",
      })
        .then(async () => {
          deleteTagRequest(tag.id).then((res) => {
            if (res.code === 200) {
              this.$message({
                type: "success",
                message: "删除成功",
              });
              if (this.activeTag === tag.id) {
                this.activeTag = "";
              }
              this.getTagDataHandle();
            }
          });
        })
        .catch(() => {});
    },
    addOrUpdateCatalog(type, data) {
      if (type === "add") {
        this.editCatalogData = {
          videoTagsId: this.activeTag,
        };
        this.operaCatalogType = "add";
      } else {
        this.editCatalogData = data;
        this.operaCatalogType = "edit";
      }
      this.showAddCatalog = true;
    },
    clickTreeNode(data) {
      this.actvieCatalog = data.id;
      this.handleSearch();
    },
    addOrUpdateCatalogChild(type, data, node) {
      if (!data.pid && type === "edit") {
        //一级目录
        this.addOrUpdateCatalog("edit", data);
        return;
      } else {
        if (type === "add") {
          this.operaType = "add";
          this.catalogEditObject = {
            pid: data.id,
            pName: data.name,
            tagId: this.activeTag,
          };
        } else {
          this.operaType = "edit";
          this.catalogEditObject = {
            ...data,
            tagId: this.activeTag,
            pName: node.parent.data.name,
          };
        }
        this.showCatalogDialog = true;
      }
    },
    delCatalog(data) {
      this.$confirm("确定删除此数据?", "提示", {
        dangerouslyUseHTMLString: true,
        confirmButtonText: "确定",
        cancelButtonText: "取消",
      })
        .then(async () => {
          deleteDirRequest(data.id).then((res) => {
            if (res.code === 200) {
              this.$message({
                type: "success",
                message: "删除成功",
              });
              if (this.actvieCatalog === data.id) {
                this.actvieCatalog = "";
              }
              this.getCatalogTree();
            }
          });
        })
        .catch(() => {});
    },
    handleSelectionChange(row) {
      this.multipleSelection = row;
    },
    handelDelete(id) {
      this.$confirm("确定取消关联此数据?", "提示", {
        dangerouslyUseHTMLString: true,
        confirmButtonText: "确定",
        cancelButtonText: "取消",
      })
        .then(async () => {
          unLinkRelRequest(id).then((res) => {
            if (res.code === 200) {
              this.$message({
                type: "success",
                message: "解除关联成功",
              });
              this.handleSearch();
            }
          });
        })
        .catch(() => {});
    },
    batchUnLink() {
      if (this.multipleSelection.length === 0) {
        this.$message({
          type: "warning",
          message: "至少选择一条数据",
        });
        return;
      }
      let ids = this.multipleSelection.map((d) => d.id);
      ids = ids.join(",");
      this.$confirm("确定取消关联此数据?", "提示", {
        dangerouslyUseHTMLString: true,
        confirmButtonText: "确定",
        cancelButtonText: "取消",
      })
        .then(async () => {
          batchUnLinkRequest(ids).then((res) => {
            if (res.code === 200) {
              this.$message({
                type: "success",
                message: "解除关联成功",
              });
              this.handleSearch();
            }
          });
        })
        .catch(() => {});
    },
    handleSearch() {
      this.searchItem.pageNum = 1;
      this.getListHandle();
    },
    handleSizeChange(val) {
      this.searchItem.pageSize = val;
      this.handleSearch();
    },
    handleCurrentChange(val) {
      this.searchItem.pageNum = val;
      this.getListHandle();
    },
    typeOptionsChange(value) {
      this.searchItem.cameraType = value;
    },

    getListHandle() {
      this.loading = true;
      this.searchItem.tagId = this.activeTag;
      this.searchItem.dirId = this.actvieCatalog;
      this.getCountData();
      getVideoTagList(this.searchItem)
        .then((res) => {
          this.loading = false;
          if (res.code === 200) {
            let tableData = res.data.records;
            this.total = Number(res.data.total);
            convertTableData(tableData).then((newData) => {
              this.tableData = newData;
            });
          }
        })
        .catch((err) => {
          this.loading = false;
        });
    },
    getCountData() {
      getVideoSiteCount(this.searchItem).then((res) => {
        if (res.code === 200) {
          this.countObject = res.data;
        } else {
          this.countObject = {
            totalCount: 0,
            countBall: 0,
            countBolt: 0,
            countHemisphere: 0,
            countCloudBolt: 0,
          };
        }
      });
    },
    closeTagHandle() {
      this.showAddTag = false;
    },
    saveTagSuccessHandle() {
      this.getTagDataHandle();
      this.closeTagHandle();
    },
    closeCatalogHandle() {
      this.showAddCatalog = false;
    },
    saveCatalogSuccessHandle() {
      this.getCatalogTree();
      this.closeCatalogHandle();
    },
    changeHandle(node) {
      if (node && node.length > 0) {
        this.searchItem.adcd = node[node.length - 1];
      } else {
        this.searchItem.adcd = "";
      }
    },
    closeChildHandle() {
      this.showCatalogDialog = false;
    },
    saveChildSuccess() {
      this.getCatalogTree();
      this.closeChildHandle();
    },
    allowDrag() {
      return true;
    },
    allowDrop(draggingNode, dropNode, type) {
      if (draggingNode.level === dropNode.level && type !== "inner") {
        let currentNode = draggingNode.data;
        let targetNode = dropNode.data;
        if (currentNode.pid === targetNode.pid) {
          return true;
        } else {
          return false;
        }
      } else {
        return false;
      }
    },
    handleDrop(draggingNode) {
      let currentNode = draggingNode.data;
      let lists = this.iterationHandle(this.catalogTree, currentNode);
      let sortParams = lists.map((d) => d.id);
      this.sortCatalogHandle(sortParams);
    },
    iterationHandle(treeData, node) {
      const targetItem = treeData.find((d) => d.id === node.id);
      if (targetItem) {
        return treeData;
      } else {
        for (let item of treeData) {
          if (item.children && item.children.length > 0) {
            const foundInChild = this.iterationHandle(item.children, node);
            if (foundInChild) {
              return foundInChild;
            }
          }
        }
      }
    },
    sortCatalogHandle(list) {
      sortDirRequest(list).then((res) => {
        if (res.code === 200) {
          this.$message({
            type: "success",
            message: "排序成功",
          });
        }
      });
    },
  },
};
</script>

<style lang="scss" scoped>
.tab-wrapper {
  display: flex;
  justify-content: flex-start;
  .tab-item {
    background: #ffffff;
    min-width: 40px;
    height: 40px;
    border-top: 4px solid #fff;
    border-radius: 4px 4px 0px 0px;
    margin-right: 10px;
    display: flex;
    justify-content: center;
    align-items: center;
    color: #666666;
    padding: 0 10px;
    cursor: pointer;
    span {
      margin-right: 5px;
    }
    i {
      font-weight: 700;
      cursor: pointer;
      color: #666666;
      padding: 5px;
      &:hover {
        background: #e3e3e3;
        border-radius: 20px;
      }
    }
  }
  .tab-item-active {
    border-top: 4px solid #0d70f2;
    color: #0d70f2;
  }
}

.page-wrapper-lf {
  height: calc(100% - 44px);
  background: #fff;
  border-top-right-radius: 8px;
  border-bottom-left-radius: 8px;
  border-bottom-right-radius: 8px;
  display: flex;
}
.page-wrapper-lf-left {
  height: calc(100% - 32px);
  width: 300px;
  margin: 16px 0;
  box-sizing: border-box;
  border-right: 1px solid #e6e6e6;
}
.btn-box {
  padding: 0 16px;
  margin-bottom: 16px;
}
.page-wrapper-lf-right {
  height: 100%;
  width: calc(100% - 300px);
}
.tree-box {
  height: calc(100% - 50px);
  overflow: auto;
}

.custom-tree-node {
  display: flex;
  justify-content: space-between;
  align-items: center;
  width: 100%;
  .custom-tree-node-label {
    max-width: 145px;
  }
  span {
    img {
      margin-right: 8px;
      width: 14px;
      height: 14px;
      &:last-child {
        // margin-right: 0;
      }
    }
  }
}
/deep/ .el-tree-node__content {
  height: 40px;
}

/deep/ .el-tree-node__label {
  padding: 0;
  height: 40px;
  line-height: 40px;
  box-sizing: border-box;
}
.search-wrapper {
  display: flex;
  align-items: center;
  justify-content: space-between;
  height: 65px;
  padding: 0 16px;
}
.header-search-box {
  display: flex;
  align-items: center;
}
.header-search-box /deep/ .el-form--inline .el-form-item {
  margin-bottom: 0;
}
.statistics-box {
  display: flex;
  align-items: center;
  height: 40px;
  background: #f0f6fc;
  margin: 0 24px 16px;
  padding: 0 16px;
  box-sizing: border-box;
}
.statistics-item {
  color: #333;
}
.statistics-num {
  color: #0d70f2;
  font-weight: bold;
}
.content-box {
  width: 100%;
  height: calc(100% - 176px);
  overflow: auto;
  padding: 0 24px;
  box-sizing: border-box;
}
.pager-box {
  padding: 8px 24px;
}
.icon-more {
  width: 16px;
  height: 16px;
  margin-top: 5px;
}
</style>