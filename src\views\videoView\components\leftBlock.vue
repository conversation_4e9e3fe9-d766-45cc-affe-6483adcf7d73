<template>
  <div class="leftBlock">
    <div
      class="videoItem"
      :class="{ active: index == activeInedex }"
      @dblclick="
        dblclickHandle(item);
        activeInedex = index;
      "
      v-for="(item, index) in videoList"
      :key="item.id"
    >
      <div>
        <img
          v-if="item.inline === '1'"
          :src="
            require('@/assets/images/icon/icon-' +
              iconObject[item.cameraType] +
              '-active.png')
          "
        />
        <img
          v-if="item.inline === '0'"
          :src="
            require('@/assets/images/icon/icon-' +
              iconObject[item.cameraType] +
              '.png')
          "
        />
      </div>
      <span class="text-limit1">{{ item.name }}</span>
    </div>
  </div>
</template>
<script>
import { getSiteExtListRequest } from "@/api/extend/extendData";
import { listStatusRequest } from "@/api/stats/statsData";
export default {
  name: "leftBlock",
  components: {},
  props: {},
  data() {
    return {
      videoList: [],
      iconObject: {
        0: "qiangji",
        1: "banqiu",
        2: "kuaiqiu",
        3: "yuntai",
        4: "qiuji",
        5: "caijishuru",
      },
      activeInedex: null,
    };
  },
  watch: {},
  computed: {},
  mounted() {
    this.getDialogvide(this.$route.query.subCode);
  },
  methods: {
    dblclickHandle(node) {
      this.$emit("leftdblclick", node);
    },
    async getDialogvide(code) {
      const res = await getSiteExtListRequest({
        pageSize: 999,
        pageNum: 1,
        extCode: "GCJS",
        dataIds: [code],
      });
      this.videoList = res.data || [];
      this.getSiteStatusHandle();
    },
    async getSiteStatusHandle() {
      let params = this.videoList.map((d) => d.id);
      const res = await listStatusRequest(params);
      let statusList = [];
      if (res.data) {
        statusList = res.data;
        let arr = [];
        this.videoList.forEach((item) => {
          statusList.forEach((jtem) => {
            if (item.id == jtem.id) {
              item = {
                ...item,
                ...{ inline: jtem.inline },
              };
              arr.push(item);
            }
          });
        });
        this.videoList = [...arr];
      }
    },
  },
};
</script>
<style lang='scss' scoped>
.leftBlock {
  height: 100%;
  overflow-y: auto;
  box-sizing: border-box;
  padding: 16px 0px;
  .videoItem {
    box-sizing: border-box;
    padding: 12px 24px;
    cursor: pointer;

    display: flex;
    align-items: center;
    &.active {
      background: #e7f1fe;
    }
    img {
      display: flex;
      justify-content: center;
      align-items: center;
      margin-right: 6px;
      width: 18px;
      height: 18px;
      &:last-child {
        // margin-right: 0;
      }
    }
    span {
      max-width: 160px;
      overflow: hidden;
      text-overflow: ellipsis;
      white-space: nowrap;
      color: #606266;
    }
  }
}
</style>
