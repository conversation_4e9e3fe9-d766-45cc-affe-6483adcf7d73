<template>
  <div class="real-time-box">
    <div class="sup-video-player" id="sup-video-player">
      <div class="video-list" :class="['video-split-' + activeTotal]">
        <div
          class="video-item"
          v-for="(item, index) in viewTotal"
          :key="item"
          @click="changeViewHandle(index)"
          :class="[
            'video-item-' + item,
            'split-' + activeTotal,
            selectIndex === index ? 'active' : '',
          ]"
        >
          <dse-video-player
            :ref="'videoRef' + index"
            v-if="previewList[index]"
            :cameraIndexCode="previewList[index] && previewList[index].cameraIndexCode"
            :keyIndex="index"
            :serverType="previewList[index] && previewList[index].jrtype"
            :name="previewList[index] && previewList[index].name"
            :transType="previewList[index] && previewList[index].transType"
            :isTool="false"
            :realtimeEnable="previewList[index] && previewList[index].realtimeEnable && isPlugins"
            :remark="previewList[index] && previewList[index].remark"
            :isShowCollect="true"
            :isCollect="previewList[index] && previewList[index].collected"
             :pageTheme="pageTheme"
            @close="closeHandle"
            @collect="collectHandle"
          ></dse-video-player>
        </div>
      </div>
    </div>
    <div class="preview-bottom">
      <div class="preview-bottom-box">
        <!-- <el-button type="primary" style="margin-right: 16px" @click="testHandle"
          >测试窗口</el-button
        > -->

        <div class="popover-box">
          <div
            v-for="item in subfieldList"
            :key="item.code"
            class="popover-icon"
            @click="changeHandle(item)"
          >
            <img
              v-if="item.code === activeSub"
              class="popover-icon-img"
              :src="
                require('@/assets/images/icon/icon-' +
                  item.code +
                  '-active.svg')
              "
              alt=""
              srcset=""
            />
            <img
              v-else-if="pageTheme === 'dark'"
              class="popover-icon-img popover-icon-img-default"
              :src="require('@/assets/images/icon/icon-' + item.code + '-fff.svg')"
              alt=""
              srcset=""
            />
            <img
              v-else
              class="popover-icon-img popover-icon-img-default"
              :src="require('@/assets/images/icon/icon-' + item.code + '.svg')"
              alt=""
              srcset=""
            />
          </div>
        </div>
        <img
          v-if="pageTheme === 'dark'"
          src="@/assets/images/icon/icon-full_fff.svg"
          class="icon-full"
          alt=""
          srcset=""
          @click="wholeFullScreen"
        />
        <img
          v-else
          src="@/assets/images/icon/icon-full.svg"
          class="icon-full"
          alt=""
          srcset=""
          @click="wholeFullScreen"
        />
      </div>
    </div>
    <video-dialog
      v-if="dialogShow"
      keyIndex="999"
      :cameraIndexCode="viewData.cameraIndexCode"
      :serverType="viewData.serverType"
      :name="viewData.name"
      @close-video="closeViewHandle"
      @close="closeDialogHandle"
    ></video-dialog>
  </div>
</template>

<script>
import videoDialog from "./videoDialog.vue";
import { collectSiteRequest, getSiteExtPageRequest } from '@/api/site/siteData'
export default {
  name: "realTimePreview",
  components: {
    videoDialog,
  },
  props: {
    isPlugins: Boolean,
    pageTheme: String
  },
  data() {
    return {
      subfieldList: [
        { code: "one", value: 1 },
        { code: "two", value: 2 },
        { code: "three", value: 3 },
        { code: "four", value: 4 },
        { code: "six", value: 6 },
        { code: "nine", value: 9 },
        { code: "sixteen", value: 16 },
      ],
      previewList: [],
      showPreview: false,
      viewTotal: [],
      activeVideoIndex: 0,
      activeTotal: 4,
      activeSub: "four",
      dialogShow: false,
      viewData: {
        cameraIndexCode: "",
        serverType: "",
        name: "",
      },
      selectIndex: 0,
    };
  },
  created() {
    this.initHandle();
  },
  methods: {
    initHandle() {
      this.viewTotal = Array.from({ length: 16 }, (_, i) => ++i);
    },
    changeHandle(item) {
      this.activeSub = item.code;
      this.activeTotal = item.value;
    },
    changeViewHandle(index) {
      this.selectIndex = index;
      // this.activeVideoIndex = index;
      this.$emit('changeIndex', index, this.previewList[index])
    },
    controlHandle(command, action, speed) {
      if (
        this.$refs["videoRef" + this.selectIndex] &&
        this.$refs["videoRef" + this.selectIndex][0]
      ) {
        this.$refs["videoRef" + this.selectIndex][0].controlHandle(
          command,
          action,
          speed
        );
      } else {
        this.$message({
          type: "warning",
          message: "请选择要控制的视频窗口",
        });
      }
    },
    gotoPresetHandle(node) {
      if (
        this.$refs["videoRef" + this.selectIndex] &&
        this.$refs["videoRef" + this.selectIndex][0]
      ) {
        this.$refs['videoRef' + this.selectIndex][0].gotoPresetHandle(node)
      } else {
        this.$message({
          type: "warning",
          message: "请选择要控制的视频窗口",
        });
      }
    },
    addRealView(node) {
      this.previewList[this.selectIndex] = node;
      this.$emit('changeIndex', this.selectIndex, this.previewList[this.selectIndex])
      this.$forceUpdate();
      this.updateVideoIndex();
    },
    updateVideoIndex() {
      for(let i = this.selectIndex; i < this.activeTotal; i++) {
        if(!this.previewList[i]) {
          this.selectIndex = i;
          return;
        }
      }
      for(let i = 0; i < this.selectIndex; i++) {
        if(!this.previewList[i]) {
          this.selectIndex = i;
          return;
        }
      }
      this.selectIndex = 0;
    },
    openAllVideo(list) {
      this.previewList = list.length > 16 ? list.slice(0, 16) : list;
      if (list.length > 9) {
        this.activeTotal = 16;
        this.activeSub = "sixteen";
      } else if (list.length > 6) {
        this.activeTotal = 9;
        this.activeSub = "nine";
      } else if (list.length > 4) {
        this.activeTotal = 6;
        this.activeSub = "six";
      } else {
        this.activeTotal = 4;
        this.activeSub = "four";
      }
      this.$forceUpdate();
    },
    closeHandle(index) {
      this.previewList[index] = null;
      this.$forceUpdate();
      // this.previewList.splice(index, 1);
      // this.activeVideoIndex = this.previewList.length;
      // this.$emit('changeIndex', this.selectIndex, this.previewList[this.selectIndex] || {})
    },
    closeViewHandle() {
      this.viewData.cameraIndexCode = "";
      this.viewData.name = "";
      this.viewData.serverType = "";
    },
    closeDialogHandle() {
      this.dialogShow = false;
      this.viewData.cameraIndexCode = "";
      this.viewData.name = "";
      this.viewData.serverType = "";
    },
    closeAllVideo() {
      this.previewList = [];
      // this.activeVideoIndex = 0;
      this.selectIndex = 0;
      this.activeTotal = 4;
      this.activeSub = "four";
      this.dialogShow = false;
      this.viewData.cameraIndexCode = "";
      this.viewData.name = "";
      this.viewData.serverType = "";
      this.$emit('changeIndex', this.selectIndex, null)
    },
    testHandle() {
      if (this.previewList[this.selectIndex]) {
        let item = this.previewList[this.selectIndex];
        this.viewData.cameraIndexCode = item.cameraIndexCode;
        this.viewData.serverType = item.jrtype;
        this.viewData.name = item.name;
        this.dialogShow = true;
      } else {
        this.$message({
          type: "warning",
          message: "请选择要观看的视频",
        });
      }
    },
    collectHandle(index, cameraIndexCode, bool) {
      collectSiteRequest(this.previewList[index].id).then(res => {
        if(res.code === 200) {
          this.previewList[index].collected = bool
          this.$message({
            type: "success",
            message: bool ? "收藏成功" : "取消收藏成功",
          })
        }
        this.$emit('collect')
      }).catch(err => {
        this.$message({
          type: "warning",
          message: "操作失败",
        });
      })
    },
    wholeFullScreen() {
      let element = document.getElementById("sup-video-player");
      if (element.requestFullscreen) {
        element.requestFullscreen();
      } else if (element.mozRequestFullScreen) {
        element.mozRequestFullScreen();
      } else if (element.webkitRequestFullscreen) {
        element.webkitRequestFullscreen();
      } else if (element.msRequestFullscreen) {
        element.msRequestFullscreen();
      }
    },
  },
};
</script>

<style lang="scss" scoped>
.real-time-box {
  height: 100%;
  border-radius: 4px;
  background: #FFF
}
.sup-video-player {
  height: calc(100% - 48px);
  padding: 0 8px;
  box-sizing: border-box;
  display: grid;
  grid-template-columns: 100%;
  grid-template-rows: calc(100%) 48px;
  position: relative;
  overflow: hidden;

  .video-list {
    flex: 1 1 auto;
    display: grid;
    .video-item {
      background: #d4e1ee
        url(data:image/png;base64,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)
        no-repeat 50%;
      cursor: pointer;
      position: relative;
      margin: 1px;
      border: 1px solid transparent;
      box-sizing: border-box;
    }
    .video-item.active {
      border: 2px solid #00e5ff;
    }
  }
  .video-list.video-split-1 {
    grid-template-columns: 100%;
    grid-template-rows: 100%;
  }

  .video-list.video-split-2 {
    grid-template-columns: 50% 50%;
    grid-template-rows: 100%;
  }
  .video-list.video-split-3 {
    grid-template-columns: 50% 50%;
    grid-template-rows: 50% 50%;
  }

  .video-list .video-item.split-3.video-item-1 {
    grid-column-start: 1;
    grid-column-end: 3;
    grid-row-start: 1;
    grid-row-end: 2;
  }
  .video-list.video-split-4 {
    grid-template-columns: 50% 50%;
    grid-template-rows: 50% 50%;
  }
  .video-list.video-split-6 {
    grid-template-columns: 33.33% 33.33% 33.33%;
    grid-template-rows: 33.33% 33.33% 33.33%;
  }
  .video-list .video-item.split-6.video-item-1 {
    grid-column-start: 1;
    grid-column-end: 3;
    grid-row-start: 1;
    grid-row-end: 3;
  }

  .video-list.video-split-9 {
    grid-template-columns: 33.33% 33.33% 33.33%;
    grid-template-rows: 33.33% 33.33% 33.33%;
  }
  .video-list.video-split-16 {
    grid-template-columns: 25% 25% 25% 25%;
    grid-template-rows: 25% 25% 25% 25%;
  }
}
.preview-bottom {
  height: 48px;
  display: flex;
  justify-content: flex-end;
  align-items: center;
  padding: 0 24px;
}
.preview-bottom-box {
  display: flex;
  align-items: center;
}
.icon-four {
  width: 24px;
  height: 24px;
  cursor: pointer;
}
.icon-full {
  width: 20px;
  height: 20px;
  margin-bottom: 4px;
  cursor: pointer;
}
.popover-icon {
  width: 24px;
  height: 24px;
  margin-right: 16px;
  cursor: pointer;
}
.popover-icon-img {
  width: 24px;
  height: 24px;
}
.popover-box {
  box-sizing: border-box;
  display: flex;
  align-items: center;
  flex-wrap: nowrap;
}
.el-popover {
  min-width: 136px;
}
.preview-item {
  width: 50%;
  height: 100%;
}
.page-wrapper-dark {
  .sup-video-player .video-item {
    background: #44719e
        url(data:image/png;base64,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)
        no-repeat 50%;
  }
  .real-time-box {
    background: #05396C;
  }
}
</style>