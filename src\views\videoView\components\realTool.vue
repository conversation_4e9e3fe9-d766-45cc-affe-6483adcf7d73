<template>
  <div class="tool-box">
    <div class="controller-direction">
      <img
        :src="require('@/assets/images/icon/bg_controller.png')"
        alt=""
        srcset=""
        class="bg_controller"
      />
      <img
        class="controller-direction-bl"
        title="左下"
        :src="require('@/assets/images/icon/icon_controller_bl.png')"
        @mousedown="controlHandle('LEFT_DOWN', 'start')"
        @mouseup="controlHandle('LEFT_DOWN', 'end')"
      />
      <img
        class="controller-direction-br"
        :src="require('@/assets/images/icon/icon_controller_br.png')"
        title="右下"
        @mousedown="controlHandle('RIGHT_DOWN', 'start')"
        @mouseup="controlHandle('RIGHT_DOWN', 'end')"
      />
      <img
        class="controller-direction-tl"
        :src="require('@/assets/images/icon/icon_controller_tl.png')"
        title="左上"
        @mousedown="controlHandle('LEFT_UP', 'start')"
        @mouseup="controlHandle('LEFT_UP', 'end')"
      />
      <img
        class="controller-direction-tr"
        :src="require('@/assets/images/icon/icon_controller_tr.png')"
        title="右上"
        @mousedown="controlHandle('RIGHT_UP', 'start')"
        @mouseup="controlHandle('RIGHT_UP', 'end')"
      />
      <img
        class="controller-direction-up"
        :src="require('@/assets/images/icon/icon_controller_up.png')"
        title="上"
        @mousedown="controlHandle('UP', 'start')"
        @mouseup="controlHandle('UP', 'end')"
      />
      <img
        class="controller-direction-down"
        :src="require('@/assets/images/icon/icon_controller_down.png')"
        title="下"
        @mousedown="controlHandle('DOWN', 'start')"
        @mouseup="controlHandle('DOWN', 'end')"
      />
      <img
        class="controller-direction-left"
        :src="require('@/assets/images/icon/icon_controller_left.png')"
        title="左"
        @mousedown="controlHandle('LEFT', 'start')"
        @mouseup="controlHandle('LEFT', 'end')"
      />
      <img
        class="controller-direction-right"
        :src="require('@/assets/images/icon/icon_controller_right.png')"
        title="右"
        @mousedown="controlHandle('RIGHT', 'start')"
        @mouseup="controlHandle('RIGHT', 'end')"
      />
    </div>
    <div
      style="
        display: flex;
        justify-content: center;
        align-items: center;
        width: 90%;
      "
    >
      <span style="margin-right: 20px" class="presets-label" :class="{'presets-label-type': type==1}">移动速度</span>
      <el-slider
        style="width: 60%"
        v-model="speed"
        :step="50"
        :min="0"
        :max="100"
        :format-tooltip="formatSpeed"
      ></el-slider>
    </div>
    <el-button-group>
      <el-button
        @mousedown.native="controlHandle('ZOOM_IN', 'start')"
        @mouseup.native="controlHandle('ZOOM_IN', 'end')"
        ><img
          v-if="pageTheme === 'dark'"
          style="margin-right: 5px"
          :src="require('@/assets/images/icon/icon_zoom_in_fff.png')"
        />
        <img
          v-else
          style="margin-right: 5px"
          :src="require('@/assets/images/icon/icon_zoom_in.png')"
        />焦距变大</el-button
      >
      <el-button
        @mousedown.native="controlHandle('ZOOM_OUT', 'start')"
        @mouseup.native="controlHandle('ZOOM_OUT', 'end')"
        ><img
          v-if="pageTheme === 'dark'"
          style="margin-right: 5px"
          :src="require('@/assets/images/icon/icon_zoom_out_fff.png')"
        /><img
        v-else
          style="margin-right: 5px"
          :src="require('@/assets/images/icon/icon_zoom_out.png')"
        />焦距变小</el-button
      >
    </el-button-group>
    <div class="presets-box" v-if="type != 1">
      <!-- <span class="presets-label">预置点：</span> -->
      <el-select
        v-model="presets"
        placeholder="请选择预置点"
        style="width: 180px; margin-right: 10px"
        @change="changePresetHandle"
      >
        <el-option
          v-for="item in presetsOptions"
          :key="item.value"
          :label="item.name"
          :value="item.value"
        >
        </el-option>
      </el-select>
      <el-button type="primary" @click="gotoPreset">预览</el-button>
      <!-- <i class="el-icon-video-play" @click="gotoPreset"></i> -->
    </div>
  </div>
</template>

<script>
// 不区分大小写
// 说明：
// LEFT 左转
// RIGHT右转
// UP 上转
// DOWN 下转
// ZOOM_IN 焦距变大
// ZOOM_OUT 焦距变小
// LEFT_UP 左上
// LEFT_DOWN 左下
// RIGHT_UP 右上
// RIGHT_DOWN 右下 FOCUS_NEAR 焦点前移
// FOCUS_FAR 焦点后移 IRIS_ENLARGE 光圈扩大
// IRIS_REDUCE 光圈缩小
// WIPER_SWITCH 接通雨刷开关
// START_RECORD_TRACK开始记录轨迹
// STOP_RECORD_TRACK 停止记录轨迹
// START_TRACK 开始轨迹
// STOP_TRACK 停止轨迹
import { inokeHaiKangRequest, inokeDaHuaRequest } from "@/api/play/playData";
export default {
  name: "toolBox",
  props: ["pageTheme", "type"],
  data() {
    return {
      speed: 50,
      presetsOptions: [],
      presets: "",
      currentPresetsNode: {},
    };
  },
  methods: {
    formatSpeed(val) {
      if (val === 0) {
        return "慢";
      }
      if (val === 50) {
        return "适中";
      }
      if (val === 100) {
        return "快";
      }
    },
    controlHandle(command, action) {
      this.$emit("control", command, action, this.speed);
    },
    getPresetHandle(node) {
      this.currentPresetsNode = {};
      this.presets = "";
      this.presetsOptions = [];
      if (!node || !node.cameraIndexCode) return;
      if (node.jrtype === "1") {
        //海康
        this.getHaikangPresetHandle(node);
      } else if (node.jrtype === "3") {
        //大华
        this.getDahuaPresetHandle(node);
      }
    },
    getHaikangPresetHandle(node) {
      let params = {
        serverId: node.videoServerId,
        url: "/api/video/v1/presets/searches",
        method: "POST",
        params: {
          cameraIndexCode: node.cameraIndexCode,
        },
      };
      inokeHaiKangRequest(params).then((res) => {
        if (res.code === 200) {
          let list = (res.data && res.data.list) || [];
          this.presetsOptions = list.map((d) => {
            return {
              ...d,
              value: d.presetPointIndex,
              name: d.presetPointName,
              serverId: node.videoServerId,
              jrtype: node.jrtype,
            };
          });
        }
      });
    },
    getDahuaPresetHandle(node) {
      let params = {
        serverId: node.videoServerId,
        url: "/evo-apigw/admin/API/DMS/Ptz/GetPresetPoints",
        method: "POST",
        params: {
          data: {
            channelId: node.cameraIndexCode,
          },
        },
      };
      inokeDaHuaRequest(params).then((res) => {
        if (res.code === 200) {
          let list = (res.data && res.data.presetPoints) || [];
          this.presetsOptions = list.map((d) => {
            return {
              ...d,
              value: d.presetPointCode,
              name: d.presetPointName,
              serverId: node.videoServerId,
              jrtype: node.jrtype,
              cameraIndexCode: d.channelId,
            };
          });
        }
      });
    },
    changePresetHandle(value) {
      this.currentPresetsNode = this.presetsOptions.find(
        (d) => d.value === value
      );
    },
    gotoPreset() {
      if (this.currentPresetsNode.value) {
        this.$emit("gotoPreset", this.currentPresetsNode);
      } else {
        this.$message({
          type: "warning",
          message: "请选择预置点",
        });
      }
    },
  },
};
</script>


<style lang="scss" scoped>
.tool-box {
  display: flex;
  justify-content: center;
  align-items: center;
  flex-direction: column;
  padding: 8px;
}
.controller-direction {
  width: 130px;
  height: 130px;
  position: relative;
  margin-bottom: 10px;
  .bg_controller {
    width: 130px;
    height: 130px;
    position: absolute;
    top: 0;
    left: 0;
  }
  .controller-direction-up {
    position: absolute;
    top: 10px;
    left: 58px;
    cursor: pointer;
    z-index: 10;
  }
  .controller-direction-down {
    position: absolute;
    bottom: 10px;
    left: 58px;
    cursor: pointer;
    z-index: 10;
  }
  .controller-direction-left {
    position: absolute;
    top: 58px;
    left: 10px;
    cursor: pointer;
    z-index: 10;
  }
  .controller-direction-right {
    position: absolute;
    top: 58px;
    right: 10px;
    cursor: pointer;
    z-index: 10;
  }
  .controller-direction-tl {
    position: absolute;
    top: 30px;
    left: 30px;
    z-index: 10;
    cursor: pointer;
  }
  .controller-direction-tr {
    position: absolute;
    top: 30px;
    right: 30px;
    z-index: 10;
    cursor: pointer;
  }
  .controller-direction-bl {
    position: absolute;
    bottom: 30px;
    left: 30px;
    z-index: 10;
    cursor: pointer;
  }
  .controller-direction-br {
    position: absolute;
    bottom: 30px;
    right: 30px;
    cursor: pointer;
  }
}
.presets-box {
  display: flex;
  align-items: center;
  margin-top: 10px;
  padding: 0 10px;
  box-sizing: border-box;
}
.presets-label {
  display: inline-block;
  width: 60px;
}
.presets-label-type {
  color: #FFF;
}
.el-icon-video-play {
  font-size: 24px;
  cursor: pointer;
  color: #666;
}
.page-wrapper-dark {
  .presets-label {
    color: #fff;
  }
  .el-icon-video-play {
    color: #FFF;
  }
}
</style>