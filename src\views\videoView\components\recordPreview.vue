<template>
  <div
    class="record-preview-box"
  >
    <div class="record-item">
      <dse-video-player
        ref="prviewRef"
        v-if="recordItem.jrtype && showRecord"
        :cameraIndexCode="recordItem.cameraIndexCode"
        :keyIndex="20"
        :serverType="recordItem.jrtype"
        :name="recordItem.name"
        :transType="recordItem.transType"
        viewType="record"
        :isTool="false"
        :pageTheme="pageTheme"
        @close="closeHandle"
      ></dse-video-player>
    </div>
  </div>
</template>

<script>
export default {
  components: {},
  props: {
    pageTheme: String
  },
  data() {
    return {
      component: null,
      recordItem: {},
      startTime: "",
      endTime: "",
      showRecord: true,
    };
  },
  methods: {
    recordPlay(preview, item) {
      this.recordItem = preview;
      this.startTime = item.startTime;
      this.endTime = item.endTime;
      this.showRecord = false;
      console.log(this.startTime, this.endTime)
      setTimeout(() => {
        this.showRecord = true;
        this.$nextTick(() => {
          this.$refs.prviewRef.recordPlay(
            this.startTime,
            this.endTime,
            preview.recordLocation
          );
        });
      }, 500);
    },
    closeHandle() {
      this.showRecord = false;
    },
  },
};
</script>


<style lang="scss" scoped>
.record-preview-box {
  width: 100%;
  height: 100%;
  padding: 0 8px 16px;
  box-sizing: border-box;
}
.record-item {
  width: 100%;
  height: 100%;
  background: #d4e1ee
    url(data:image/png;base64,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)
    no-repeat 50%;
  cursor: pointer;
  position: relative;
}
.page-wrapper-dark {
  .record-item {
    background: #44719e
      url(data:image/png;base64,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)
      no-repeat 50%;
  }
}
</style>