<template>
  <div class="tool-box" :class="{ 'tool-box-inline': layout === 'inline' }">
    <el-form
      ref="formData"
      label-width="80px"
      :inline="layout === 'inline'"
      :rules="rules"
      :model="formData"
    >
      <el-form-item label="录像日期" prop="date">
        <el-date-picker
          v-model="formData.date"
          type="date"
          placeholder="选择日期"
          style="width: 200px"
          value-format="yyyy-MM-dd"
          popper-class="dse-date-picker"
          @change="changeDateHandle"
        >
        </el-date-picker>
      </el-form-item>
      <el-form-item label="录像时间" prop="timeRange">
        <el-time-picker
          is-range
          v-model="formData.timeRange"
          range-separator="-"
          start-placeholder="开始时间"
          end-placeholder="结束时间"
          placeholder="选择时间范围"
          style="width: 200px"
          value-format="HH:mm:ss"
          popper-class="dse-time-picker"
          @change="changeTimeHandle"
        >
        </el-time-picker>
      </el-form-item>
      <el-form-item label-width="0px">
        <el-button type="primary" class="btn" @click="searchHandle"
          >查询</el-button
        >
      </el-form-item>
    </el-form>
  </div>
</template>

<script>
export default {
  props: {
    layout: {
      type: String,
      default: "block",
    },
  },
  data() {
    return {
      formData: {
        date: "",
        timeRange: null,
      },
      rules: {
        date: [{ required: true, message: "请选择录像日期", trigger: "blur" }],
        timeRange: [
          { required: true, message: "请选择录像时间", trigger: "blur" },
        ],
      },
    };
  },
  methods: {
    changeDateHandle(val) {
      if (
        this.formData.timeRange &&
        this.formData.timeRange.length > 0 &&
        val
      ) {
        let params = {
          startTime: this.formData.date + " " + this.formData.timeRange[0],
          endTime: this.formData.date + " " + this.formData.timeRange[1],
        };
        this.$emit("change", params);
      } else {
        this.$emit("change", {
          startTime: '',
          endTime: '',
        });
      }
    },
    changeTimeHandle() {
      if (
        this.formData.timeRange &&
        this.formData.timeRange.length > 0 &&
        this.formData.date
      ) {
        let params = {
          startTime: this.formData.date + " " + this.formData.timeRange[0],
          endTime: this.formData.date + " " + this.formData.timeRange[1],
        };
        this.$emit("change", params);
      } else {
        this.$emit("change", {
          startTime: '',
          endTime: '',
        });
      }
    },
    searchHandle() {
      this.$refs["formData"].validate((valid) => {
        if (valid) {
          let params = {
            startTime: this.formData.date + " " + this.formData.timeRange[0],
            endTime: this.formData.date + " " + this.formData.timeRange[1],
          };
          this.$emit("search", params);
        }
      });
    },
  },
};
</script>


<style lang="scss" scoped>
.tool-box {
  display: flex;
  justify-content: center;
  align-items: center;
  flex-direction: column;
  padding: 8px;
}
.tool-box-inline {
  align-items: flex-start;
}
.tool-box-inline /deep/ .el-form-item {
  margin-bottom: 10px;
}
.btn {
  width: 100%;
}
.page-wrapper-dark {
  .tool-box {
    /deep/ .el-form-item__label {
      color: #fff;
    }
    /deep/ .el-range-input {
      background: #05396c;
      color: #fff;
    }
  }
}
</style>
<style>
.dse-date-picker {
  left: 5px !important;
}
.dse-time-picker {
  left: -5px !important;
}
</style>