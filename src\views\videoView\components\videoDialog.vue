<template>
  <el-dialog
    :title="title"
    :visible.sync="dialogVisible"
    width="860px"
    :before-close="handleClose"
    :close-on-click-modal="false"
  >
    <div class="player-box">
      <dse-video-player
        :cameraIndexCode="cameraIndexCode"
        :keyIndex="keyIndex"
        :serverType="serverType"
        :name="name"
        @close="closePlayerHandle"
      ></dse-video-player>
    </div>
  </el-dialog>
</template>

<script>
// import DseVideoPlayer from "@/components/DseVideoPlayer/index.vue";
import DseVideoPlayer from 'dse-video-player'
export default {
  components: {
    DseVideoPlayer,
  },
  props: {
    cameraIndexCode: {
      type: [String, Number],
      default: "",
    },
    keyIndex: [String, Number],
    serverType: [String, Number],
    name: String
  },
  data() {
    return {
      title: "查看视频",
      dialogVisible: true,
    };
  },
  mounted() {
    this.dialogVisible = true;
  },
  methods: {
    handleClose() {
      this.$emit("close");
    },
    closePlayerHandle(index, code) {
      this.$emit('close-video', index, code)
    }
  },
};
</script>

<style lang="scss" scoped>
.player-box {
  width: 100%;
  height: 40vh;
}
</style>