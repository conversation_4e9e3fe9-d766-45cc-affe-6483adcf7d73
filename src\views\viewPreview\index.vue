<template>
  <div class="view-box">
    <dse-video-player
      :ref="'videoRef'"
      v-if="cameraIndexCode"
      :cameraIndexCode="cameraIndexCode"
      :keyIndex="keyIndex"
      :serverType="serverType"
      :name="name"
      :transType="transType"
      :isTool="false"
      :viewType="viewType"
      :isClose="false"
      :realtimeEnable="realtimeEnable"
      :remark="remark"
      @close="closeHandle"
    ></dse-video-player>
    <div class="real-tool-box" v-if="isTool && cameraIndexCode">
      <real-tool type="1" :pageTheme="pageTheme" @control="controlHandle"></real-tool>
    </div>
  </div>
</template>

<script>
import realTool from "@/views/videoView/components/realTool.vue";
import CameraControllerFactory from "@/utils/plugins/controlCamera";
import { serverOptions } from "@/utils/index";
export default {
  name: "viewPreview",
  components: {
    realTool,
  },
  data() {
    return {
      keyIndex: 1,
      cameraIndexCode: "",
      serverType: "",
      name: "",
      transType: "0",
      isTool: true,
      viewType: "real",
      startTime: "",
      endTime: "",
      recordLocation: "",
      realtimeEnable: false,
      remark: "",
      pageTheme: "light",
      controller: null,
    };
  },
  mounted() {
    this.cameraIndexCode = this.$route.query.cameraIndexCode || "";
    this.serverType = this.$route.query.serverType || "";
    this.name = this.$route.query.name || "";
    this.transType = this.$route.query.transType || "0";
    this.isTool = this.$route.query.isTool === "1";
    this.viewType = this.$route.query.viewType || "real";
    this.startTime = this.$route.query.startTime || "";
    this.endTime = this.$route.query.endTime || "";
    this.recordLocation = this.$route.query.recordLocation || "";
    this.realtimeEnable = this.$route.query.realtimeEnable == "1";
    this.remark = this.$route.query.remark || "";
    if (!this.cameraIndexCode) {
      console.error("cameraIndexCode不能为空");
      return;
    }
    if (!this.serverType) {
      console.error("serverType不能为空");
      return;
    }
    if (this.viewType === "record") {
      this.isTool = false;
      if (!this.startTime) {
        console.error("回放时，startTime不能为空");
        return;
      }
      if (!this.endTime) {
        console.error("回放时，endTime不能为空");
        return;
      }
      this.recordPlay();
    }
    this.initController()
  },
  methods: {
    closeHandle() {
      this.cameraIndexCode = "";
    },
    initController() {
      console.log(6763, this.serverType, serverOptions[this.serverType])
      this.controller = CameraControllerFactory.createController(
        serverOptions[this.serverType]
      );
    },
    controlHandle(command, action, speed) {
      console.log(7323, this.controller)
      if (this.controller && this.cameraIndexCode) {
        this.controller.controlCamera(
          command,
          action,
          speed,
          this.cameraIndexCode
        );
      }
    },
    recordPlay() {
      setTimeout(() => {
        if (!this.startTime) {
          console.error("startTime不能为空");
          return "";
        }
        if (!this.endTime) {
          console.error("endTime不能为空");
          return "";
        }
        this.$nextTick(() => {
          this.$refs.videoRef.recordPlay(
            this.startTime,
            this.endTime,
            this.recordLocation
          );
        });
      }, 500);
    },
  },
};
</script>

<style lang="scss" scoped>
.view-box {
  width: 100%;
  height: 100%;
  overflow: hidden;
  position: relative;
}

.real-tool-box {
  position: absolute;
  right: 20px;
  bottom: 20px;
  z-index: 100;
}
</style>