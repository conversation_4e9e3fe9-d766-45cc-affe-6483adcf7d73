<template>
  <div
    class="hai-kang-plugin-view"
    :id="'haiKangPlugin' + keyIndex"
    :ref="'haiKangPlugin' + keyIndex"
  >
    <div
      :ref="'playWnd' + keyIndex"
      class="playWnd"
      :id="'playWnd' + keyIndex"
    ></div>
  </div>
</template>

<script>
import { getSecureConfByCodeRequest } from "@/api/play/playData";
import dayjs from "dayjs";
export default {
  name: "HaiKangPluginView",
  data() {
    return {
      searverConfig: {},
      oWebControl: null,
      initCount: 0, // 插件初始化次数
      pubKey: "",
      width: 0,
      height: 0,
      keyIndex: 0,
      cameraIndexCode: "",
      viewType: "real",
    };
  },
  watch: {
    // cameraIndexCode: {
    //   handler() {
    //     console.log("cameraIndexCode", this.cameraIndexCode);
    //     this.getConfigHandle();
    //   },
    //   immediate: true,
    //   deep: true,
    // },
  },
  mounted() {
    let that = this;
    this.cameraIndexCode = that.$route.query.cameraIndexCode;
    this.type = that.$route.query.type;
    if(this.cameraIndexCode) {
      this.getConfigHandle();
    }
    this.$nextTick(() => {
      const resizeObserver = new ResizeObserver((entries) => {
        for (let entry of entries) {
          const { width, height } = entry.contentRect;
          that.width = width;
          that.height = height;
          console.log("resizeObserver", that.width, that.height);
          that.resizeHandle();
        }
        that.$forceUpdate();
      });
      resizeObserver.observe(this.$refs["haiKangPlugin" + that.keyIndex]);
    });
  },
  beforeDestroy() {
    if (this.oWebControl) {
      this.oWebControl.JS_HideWnd();
    }
  },
  methods: {
    getConfigHandle() {
      getSecureConfByCodeRequest({
        cameraIndexCode: this.cameraIndexCode,
      }).then((res) => {
        if (res.code === 200) {
          this.searverConfig = res.data;
          this.initPlugin();
        }
      });
    },
    initPlugin() {
      let that = this;
      this.oWebControl = new WebControl({
        szPluginContainer: "playWnd" + that.keyIndex, // 指定容器id
        iServicePortStart: 15900, // 指定起止端口号，建议使用该值
        iServicePortEnd: 15900,
        szClassId: "23BF3B0A-2C56-4D97-9C03-0CB103AA8F11", // 用于IE10使用ActiveX的clsid
        cbConnectSuccess: function () {
          // 创建WebControl实例成功
          that.oWebControl
            .JS_StartService("window", {
              // WebControl实例创建成功后需要启动服务
              dllPath: "./VideoPluginConnect.dll", // 值"./VideoPluginConnect.dll"写死
            })
            .then(
              function () {
                // 启动插件服务成功
                console.log("启动插件服务成功");
                that.oWebControl.JS_SetWindowControlCallback({
                  // 设置消息回调
                  cbIntegrationCallBack: that.cbIntegrationCallBack,
                });

                that.oWebControl
                  .JS_CreateWnd(
                    "playWnd" + that.keyIndex,
                    that.width,
                    that.height
                  )
                  .then(function () {
                    //JS_CreateWnd创建视频播放窗口，宽高可设定
                    that.init(); // 创建播放实例成功后初始化
                  });
              },
              function () {
                // 启动插件服务失败
              }
            );
        },
        cbConnectError: function () {
          // 创建WebControl实例失败
          this.oWebControl = null;
          // $("#playWnd").html("插件未启动，正在尝试启动，请稍候...");
          that.$message.error("插件未启动，正在尝试启动，请稍候...");
          WebControl.JS_WakeUp("VideoWebPlugin://"); // 程序未启动时执行error函数，采用wakeup来启动程序
          that.initCount++;
          if (that.initCount < 3) {
            setTimeout(function () {
              that.initPlugin();
            }, 3000);
          } else {
            // $("#playWnd").html("插件启动失败，请检查插件是否安装！");
            that.$message.error("插件启动失败，请检查插件是否安装！");
          }
        },
        cbConnectClose: function (bNormalClose) {
          // 异常断开：bNormalClose = false
          // JS_Disconnect正常断开：bNormalClose = true
          console.log("cbConnectClose");
          that.oWebControl = null;
          // $("#playWnd").html("插件未启动，正在尝试启动，请稍候...");
          that.$message.error("插件未启动，正在尝试启动，请稍候...");
          WebControl.JS_WakeUp("VideoWebPlugin://");
          that.initCount++;
          if (that.initCount < 3) {
            setTimeout(function () {
              that.initPlugin();
            }, 3000);
          } else {
            // $("#playWnd").html("插件启动失败，请检查插件是否安装！");
            that.$message.error("插件启动失败，请检查插件是否安装！");
          }
        },
      });
    },
    confirmPluginInstall() {
      this.$confirm("插件启动失败或未安装，是否安装插件？", "提示", {
        confirmButtonText: "确定",
        cancelButtonText: "取消",
        type: "warning",
      }).then(() => {
        window.location.href = "/static/haiKangPlugin/VideoWebPlugin.exe";
      });
    },
    cbIntegrationCallBack(oData) {
      let type = oData.responseMsg.type;
      let that = this;
      console.log("cbIntegrationCallBack", oData, type);
      switch (type) {
        case 1:
          //鼠标选中当前窗口
          // this.currentVideo = JSON.parse(oData.responseMsg.msg);
          // this.currentVideo = oData.responseMsg.msg;
          break;
        case 2:
          that.oWebControl
            .JS_RequestInterface({
              funcName: "getLayout",
            })
            .then(function (layData) {
              if (layData.responseMsg.code === 0) {
                let data = JSON.parse(layData.responseMsg.data);
                if (data.layout === "1x1") {
                  // that.currentVideo = JSON.parse(oData.responseMsg.msg);
                  // that.currentVideo = oData.responseMsg.msg;
                }
              }
            });
          break;
      }
      let result = oData.responseMsg.msg.result;
      console.log("cbIntegrationCallBack ==> result", result);
      switch (result) {
        case 816:
          this.$emit("close");
          break;
        case 1:
          break;
      }
    },
    init() {
      let that = this;
      let playMode = 0; //初始播放模式：0-预览，1-回放
      let showToolbar = 0; //是否显示工具栏，0-不显示，非0-显示
      if (that.viewType === "real") {
        playMode = 0;
        showToolbar = 0;
      } else {
        playMode = 1;
        showToolbar = 1;
      }
      that.getPubKey(function () {
        console.log("searverConfig", that.searverConfig);
        ////////////////////////////////// 请自行修改以下变量值	////////////////////////////////////
        var appkey = that.searverConfig.appKey; //综合安防管理平台提供的appkey，必填
        var secret = that.setEncrypt(that.searverConfig.appSecret); //综合安防管理平台提供的secret，必填
        var ip = that.searverConfig.ip; //综合安防管理平台IP地址，必填
        // var playMode = 0; //初始播放模式：0-预览，1-回放
        var port = Number(that.searverConfig.host); //综合安防管理平台端口，若启用HTTPS协议，默认443
        var snapDir = "D:\\SnapDir"; //抓图存储路径
        var videoDir = "D:\\VideoDir"; //紧急录像或录像剪辑存储路径
        var layout = "1x1"; //playMode指定模式的布局
        var enableHTTPS = 1; //是否启用HTTPS协议与综合安防管理平台交互，这里总是填1
        var encryptedFields = "secret"; //加密字段，默认加密领域为secret
        // var showToolbar = ; //是否显示工具栏，0-不显示，非0-显示
        var showSmart = 1; //是否显示智能信息（如配置移动侦测后画面上的线框），0-不显示，非0-显示
        var buttonIDs =
          "0,16,256,257,258,259,260,512,513,514,515,516,517,768,769"; //自定义工具条按钮
        var toolBarButtonIDs =
          "2048,2049,2050,2304,2306,2305,2307,2308,2309,4096,4608,4099,4098,4100";
        ////////////////////////////////// 请自行修改以上变量值	////////////////////////////////////
        console.log("init", {
          appkey: appkey, //API网关提供的appkey
          secret: secret, //API网关提供的secret
          ip: ip, //API网关IP地址
          playMode: playMode, //播放模式（决定显示预览还是回放界面）
          port: port, //端口
          snapDir: snapDir, //抓图存储路径
          videoDir: videoDir, //紧急录像或录像剪辑存储路径
          layout: layout, //布局
          enableHTTPS: enableHTTPS, //是否启用HTTPS协议
          encryptedFields: encryptedFields, //加密字段
          showToolbar: showToolbar, //是否显示工具栏
          showSmart: showSmart, //是否显示智能信息
          buttonIDs: buttonIDs, //自定义工具条按钮
          toolBarButtonIDs: toolBarButtonIDs, //自定义工具条按钮
        });
        that.oWebControl
          .JS_RequestInterface({
            funcName: "init",
            argument: JSON.stringify({
              appkey: appkey, //API网关提供的appkey
              secret: secret, //API网关提供的secret
              ip: ip, //API网关IP地址
              playMode: playMode, //播放模式（决定显示预览还是回放界面）
              port: port, //端口
              snapDir: snapDir, //抓图存储路径
              videoDir: videoDir, //紧急录像或录像剪辑存储路径
              layout: layout, //布局
              enableHTTPS: enableHTTPS, //是否启用HTTPS协议
              encryptedFields: encryptedFields, //加密字段
              showToolbar: showToolbar, //是否显示工具栏
              showSmart: showSmart, //是否显示智能信息
              buttonIDs: buttonIDs, //自定义工具条按钮
              toolBarButtonIDs: toolBarButtonIDs, //自定义工具条按钮
            }),
          })
          .then(function (oData) {
            console.log("init", oData, that.width, that.height);
            that.oWebControl.JS_Resize(that.width, that.height); // 初始化后resize一次，规避firefox下首次显示窗口后插件窗口未与DIV窗口重合问题
            if (that.viewType === "real") {
              that.startPreview();
            } else {
              that.playBackHandle();
            }
          });
      });
    },
    setEncrypt(value) {
      var encrypt = new JSEncrypt();
      encrypt.setPublicKey(this.pubKey);
      return encrypt.encrypt(value);
    },

    //获取公钥
    getPubKey(callback) {
      let that = this;
      that.oWebControl
        .JS_RequestInterface({
          funcName: "getRSAPubKey",
          argument: JSON.stringify({
            keyLength: 1024,
          }),
        })
        .then(function (oData) {
          console.log("getPubKey", oData, that.initCount);
          if (oData.responseMsg.data) {
            that.pubKey = oData.responseMsg.data;
            callback();
          }
        });
    },
    startPreview() {
      let that = this;
      var cameraIndexCode = that.cameraIndexCode; //获取输入的监控点编号值，必填
      var streamMode = 0; //主子码流标识：0-主码流，1-子码流
      var transMode = 1; //传输协议：0-UDP，1-TCP
      var gpuMode = 0; //是否启用GPU硬解，0-不启用，1-启用
      var wndId = -1; //播放窗口序号（在2x2以上布局下可指定播放窗口）

      cameraIndexCode = cameraIndexCode.replace(/(^\s*)/g, "");
      cameraIndexCode = cameraIndexCode.replace(/(\s*$)/g, "");

      that.oWebControl
        .JS_RequestInterface({
          funcName: "startPreview",
          argument: JSON.stringify({
            cameraIndexCode: cameraIndexCode, //监控点编号
            streamMode: streamMode, //主子码流标识
            transMode: transMode, //传输协议
            gpuMode: gpuMode, //是否开启GPU硬解
            wndId: wndId, //可指定播放窗口
          }),
        })
        .then(function (oData) {
          console.log("startPreview", oData);
        });
    },
    playBackHandle() {
      let startTime = dayjs().format("YYYY-MM-DD");
      startTime = startTime + " 00:00:00";
      let endTime = dayjs().format("YYYY-MM-DD HH:mm:ss");
      var cameraIndexCode = this.cameraIndexCode; //获取输入的监控点编号值，必填
      var startTimeStamp = new Date(
        startTime.replace("-", "/").replace("-", "/")
      ).getTime(); //回放开始时间戳，必填
      var endTimeStamp = new Date(
        endTime.replace("-", "/").replace("-", "/")
      ).getTime(); //回放结束时间戳，必填
      var recordLocation = 0; //录像存储位置：0-中心存储，1-设备存储
      var transMode = 1; //传输协议：0-UDP，1-TCP
      var gpuMode = 0; //是否启用GPU硬解，0-不启用，1-启用
      var wndId = -1; //播放窗口序号（在2x2以上布局下可指定播放窗口）

      this.oWebControl.JS_RequestInterface({
        funcName: "startPlayback",
        argument: JSON.stringify({
          cameraIndexCode: cameraIndexCode, //监控点编号
          startTimeStamp: Math.floor(startTimeStamp / 1000).toString(), //录像查询开始时间戳，单位：秒
          endTimeStamp: Math.floor(endTimeStamp / 1000).toString(), //录像结束开始时间戳，单位：秒
          recordLocation: recordLocation, //录像存储类型：0-中心存储，1-设备存储
          transMode: transMode, //传输协议：0-UDP，1-TCP
          gpuMode: gpuMode, //是否启用GPU硬解，0-不启用，1-启用
          wndId: wndId, //可指定播放窗口
        }),
      });
    },
    resizeHandle() {
      if (this.oWebControl) {
        this.oWebControl.JS_Resize(this.width, this.height);
      }
    },
  },
};
</script>
<style lang="scss" scoped>
.hai-kang-plugin-view {
  width: 100vw;
  height: 100vh;
  position: relative;
  .playWnd {
    width: 100%;
    height: 100%;
    position: absolute;
    top: 0;
    left: 0;
  }
}
</style>

