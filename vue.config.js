const { defineConfig } = require('@vue/cli-service')
const ip = 'http://***********:8090'
module.exports = defineConfig({
  publicPath: process.env.NODE_ENV === 'production' ? '/dse-video-web/' : '/dse-video-web/',
  transpileDependencies: true,
  outputDir: 'dse-video-web',
  lintOnSave: false,
  devServer: {
    port: 8080,
    open: true,
    host: '0.0.0.0',
    proxy: {
      '/oauth': {
        target: ip,
        // target: 'http://************:30090',
        changeOrigin: true,
        pathRewrite: {
          '^/oauth': '/oauth'
        }
      },
      '/video': {
        target: ip,
        // target: 'http://***********:8023',
        // target: 'http://whdse.cn:56324/',
        changeOrigin: true,
        pathRewrite: {
          '^/video': '/video'
        }
      },
      '/dsevideo': {
        target: ip,
        changeOrigin: true,
        pathRewrite: {
          '^/dsevideo': '/dsevideo'
        }
      },
      '/yingShi': {
        target: 'http://whdse.cn:56324/',
        // target: 'http://************:8200',
        changeOrigin: true,
        pathRewrite: {
          '^/yingShi': '/'
        }
      },
      '/haiKang': {
        target: 'http://whdse.cn:56324/',
        // target: 'http://************:8200',
        changeOrigin: true,
        pathRewrite: {
          '^/haiKang': '/'
        }
      },
      '/baseplatform/rs/': {
        target: ip,
        // target: 'http://************:8200/baseplatform',
        // target: 'http://***************:22403/baseplatform',
        changeOrigin: true,
        pathRewrite: {
          '^/baseplatform/rs/': '/baseplatform/rs/'
        }
      },
    }
  }
})
